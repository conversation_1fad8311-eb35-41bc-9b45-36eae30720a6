import {
    CognitoIdentityProviderClient,
    AdminCreateUserCommand,
    AdminSetUserPasswordCommand,
    AdminInitiateAuthCommand,
    type AdminInitiateAuthCommandInput,
    type AuthFlowType,
    MessageActionType
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand,
    QueryCommand,
    UpdateCommand,
    type QueryCommandInput
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import * as jwt from 'jsonwebtoken';
import * as jose from 'node-jose';

// Types
interface AppleSignInRequest {
    identityToken: string;
    authorizationCode: string;
    user?: {
        name?: {
            firstName?: string;
            lastName?: string;
        };
        email?: string;
    };
}

interface AppleTokenPayload {
    iss: string;
    aud: string;
    exp: number;
    iat: number;
    sub: string;
    email?: string;
    email_verified?: boolean;
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    cognitoUserId: string;
    appleUserId?: string;
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;
const APPLE_TEAM_ID = process.env.APPLE_TEAM_ID;
const APPLE_CLIENT_ID = process.env.APPLE_CLIENT_ID;
const APPLE_KEY_ID = process.env.APPLE_KEY_ID;
const APPLE_PRIVATE_KEY = process.env.APPLE_PRIVATE_KEY;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'OPTIONS,POST,GET'
    },
    body: JSON.stringify(body)
});

// Verify Apple identity token
const verifyAppleToken = async (identityToken: string): Promise<AppleTokenPayload> => {
    try {
        // Fetch Apple's public keys
        const response = await fetch('https://appleid.apple.com/auth/keys');
        const keys = await response.json() as { keys: any[] };

        // Decode the token header to get the key ID
        const tokenHeader = jwt.decode(identityToken, { complete: true });
        if (!tokenHeader || typeof tokenHeader === 'string') {
            throw new Error('Invalid token format');
        }

        const kid = tokenHeader.header.kid;
        const key = keys.keys.find((k: any) => k.kid === kid);

        if (!key) {
            throw new Error('Apple public key not found');
        }

        // Convert JWK to PEM format using node-jose
        const keyStore = jose.JWK.createKeyStore();
        const jwkKey = await keyStore.add(key);
        const publicKey = jwkKey.toPEM();

        // Verify and decode the token
        const payload = jwt.verify(identityToken, publicKey, {
            algorithms: ['RS256'],
            audience: APPLE_CLIENT_ID,
            issuer: 'https://appleid.apple.com'
        }) as AppleTokenPayload;

        return payload;
    } catch (error) {
        console.error('Apple token verification failed:', error);
        throw new Error('Invalid Apple identity token');
    }
};



// Find existing user by Apple ID
const findUserByAppleId = async (appleUserId: string): Promise<UserRecord | null> => {
    try {
        const params: QueryCommandInput = {
            TableName: USERS_TABLE,
            IndexName: 'AppleUserIdIndex', // You'll need to create this GSI
            KeyConditionExpression: 'appleUserId = :appleUserId',
            ExpressionAttributeValues: {
                ':appleUserId': appleUserId
            }
        };

        const result = await dynamodb.send(new QueryCommand(params));
        return result.Items?.[0] as UserRecord || null;
    } catch (error) {
        console.error('Error finding user by Apple ID:', error);
        return null;
    }
};

// Find existing user by email
const findUserByEmail = async (email: string): Promise<UserRecord | null> => {
    try {
        const params: QueryCommandInput = {
            TableName: USERS_TABLE,
            IndexName: 'EmailIndex',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email.toLowerCase()
            }
        };

        const result = await dynamodb.send(new QueryCommand(params));
        return result.Items?.[0] as UserRecord || null;
    } catch (error) {
        console.error('Error finding user by email:', error);
        return null;
    }
};

// Create or update user in Cognito
const createOrUpdateCognitoUser = async (email: string, appleUserId: string): Promise<string> => {
    try {
        // Generate a temporary password for Cognito user
        const tempPassword = `TempApple${Math.random().toString(36).substring(2)}!`;

        const createUserParams = {
            UserPoolId: USER_POOL_ID,
            Username: email,
            TemporaryPassword: tempPassword,
            MessageAction: MessageActionType.SUPPRESS, // Don't send welcome email
            UserAttributes: [
                {
                    Name: 'email',
                    Value: email
                },
                {
                    Name: 'email_verified',
                    Value: 'true'
                }
            ]
        };

        const createResult = await cognitoClient.send(new AdminCreateUserCommand(createUserParams));
        const cognitoUserId = createResult.User?.Username;

        if (!cognitoUserId) {
            throw new Error('Failed to create Cognito user');
        }

        // Set permanent password
        const setPasswordParams = {
            UserPoolId: USER_POOL_ID,
            Username: cognitoUserId,
            Password: tempPassword,
            Permanent: true
        };

        await cognitoClient.send(new AdminSetUserPasswordCommand(setPasswordParams));

        return cognitoUserId;
    } catch (error: any) {
        if (error.name === 'UsernameExistsException') {
            // User already exists in Cognito, return the existing username
            return email;
        }
        throw error;
    }
};

// Create user record in DynamoDB
const createUserRecord = async (
    email: string,
    cognitoUserId: string,
    appleUserId: string,
    firstName?: string,
    lastName?: string
): Promise<UserRecord> => {
    const userId = uuidv4();
    const now = new Date().toISOString();

    const userRecord: UserRecord = {
        id: userId,
        email: email.toLowerCase(),
        firstName: firstName || '',
        lastName: lastName || '',
        cognitoUserId,
        appleUserId,
        createdAt: now,
        updatedAt: now
    };

    const params = {
        TableName: USERS_TABLE,
        Item: userRecord
    };

    await dynamodb.send(new PutCommand(params));
    return userRecord;
};

// Authenticate with Cognito to get tokens for Apple Sign In users
const authenticateWithCognito = async (email: string, cognitoUserId: string): Promise<any> => {
    try {
        // For Apple Sign In users, we need to use admin authentication
        // since we don't have their password. We'll set a temporary password
        // and then authenticate with it.
        const tempPassword = `TempApple${Math.random().toString(36).substring(2)}!A1`;

        // Set the password for the user
        const setPasswordParams = {
            UserPoolId: USER_POOL_ID,
            Username: cognitoUserId,
            Password: tempPassword,
            Permanent: true
        };

        await cognitoClient.send(new AdminSetUserPasswordCommand(setPasswordParams));

        // Now authenticate with the temporary password
        const authParams: AdminInitiateAuthCommandInput = {
            UserPoolId: USER_POOL_ID,
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'ADMIN_USER_PASSWORD_AUTH' as AuthFlowType,
            AuthParameters: {
                USERNAME: email,
                PASSWORD: tempPassword
            }
        };

        const authResult = await cognitoClient.send(new AdminInitiateAuthCommand(authParams));
        return authResult.AuthenticationResult;
    } catch (error) {
        console.error('Cognito authentication failed:', error);
        throw new Error('Authentication failed');
    }
};

// Main Apple Sign In handler
export const appleSignIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { identityToken, authorizationCode, user }: AppleSignInRequest = JSON.parse(event.body);

        if (!identityToken) {
            return createResponse(400, { error: 'Identity token is required' });
        }

        // Verify required environment variables
        if (!USER_POOL_ID || !USER_POOL_CLIENT_ID || !USERS_TABLE) {
            return createResponse(500, { error: 'Server configuration missing' });
        }

        if (!APPLE_TEAM_ID || !APPLE_CLIENT_ID || !APPLE_KEY_ID) {
            return createResponse(500, { error: 'Apple configuration missing' });
        }

        // Verify Apple identity token
        const applePayload = await verifyAppleToken(identityToken);
        const appleUserId = applePayload.sub;
        const email = applePayload.email || user?.email;

        if (!email) {
            return createResponse(400, { error: 'Email is required for Apple Sign In' });
        }

        // Check if user already exists by Apple ID
        let existingUser = await findUserByAppleId(appleUserId);

        if (!existingUser) {
            // Check if user exists by email
            existingUser = await findUserByEmail(email);

            if (existingUser) {
                // Link Apple ID to existing account
                existingUser.appleUserId = appleUserId;
                existingUser.updatedAt = new Date().toISOString();

                const updateParams = {
                    TableName: USERS_TABLE,
                    Key: { id: existingUser.id },
                    UpdateExpression: 'SET appleUserId = :appleUserId, updatedAt = :updatedAt',
                    ExpressionAttributeValues: {
                        ':appleUserId': appleUserId,
                        ':updatedAt': existingUser.updatedAt
                    }
                };

                await dynamodb.send(new UpdateCommand(updateParams));
            } else {
                // Create new user
                const cognitoUserId = await createOrUpdateCognitoUser(email, appleUserId);
                existingUser = await createUserRecord(
                    email,
                    cognitoUserId,
                    appleUserId,
                    user?.name?.firstName,
                    user?.name?.lastName
                );
            }
        }

        // Authenticate with Cognito to get tokens
        const authResult = await authenticateWithCognito(email, existingUser.cognitoUserId);

        if (!authResult) {
            return createResponse(500, { error: 'Failed to generate authentication tokens' });
        }

        // Check if username is required
        const requiresUsername = !existingUser.username || existingUser.username.trim() === '';

        return createResponse(200, {
            message: 'Apple Sign In successful',
            user: {
                id: existingUser.id,
                email: existingUser.email,
                username: existingUser.username,
                firstName: existingUser.firstName,
                lastName: existingUser.lastName
            },
            tokens: {
                accessToken: authResult.AccessToken,
                refreshToken: authResult.RefreshToken,
                idToken: authResult.IdToken
            },
            requiresUsername
        });

    } catch (error: any) {
        console.error('Apple Sign In error:', error);
        return createResponse(500, {
            error: 'Apple Sign In failed',
            message: error.message || 'An unexpected error occurred'
        });
    }
};

export { verifyAppleToken, findUserByAppleId, findUserByEmail, createResponse };
