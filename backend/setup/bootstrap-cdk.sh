#!/bin/bash

# GameFlex CDK Bootstrap Setup Script
# This script helps you bootstrap CDK for different environments with proper feedback

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Account ID
ACCOUNT_ID="************"

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  GameFlex CDK Bootstrap Setup"
    echo "=============================================="
    echo -e "${NC}"
}

# Function to load environment variables from .env file
load_env_vars() {
    local env_suffix=$1
    local env_file="setup/.env"

    if [[ ! -f "$env_file" ]]; then
        print_error ".env file not found at $env_file"
        print_info "Please create a .env file with your AWS credentials:"
        print_info "AWS_ACCESS_KEY_ID_DEV=your_dev_access_key"
        print_info "AWS_SECRET_ACCESS_KEY_DEV=your_dev_secret_key"
        print_info "AWS_DEFAULT_REGION_DEV=us-west-2"
        print_info ""
        print_info "AWS_ACCESS_KEY_ID_STAGING=your_staging_access_key"
        print_info "AWS_SECRET_ACCESS_KEY_STAGING=your_staging_secret_key"
        print_info "AWS_DEFAULT_REGION_STAGING=us-west-2"
        print_info ""
        print_info "AWS_ACCESS_KEY_ID_PROD=your_prod_access_key"
        print_info "AWS_SECRET_ACCESS_KEY_PROD=your_prod_secret_key"
        print_info "AWS_DEFAULT_REGION_PROD=us-east-1"
        exit 1
    fi

    # Source the .env file
    set -a  # automatically export all variables
    source "$env_file"
    set +a  # stop automatically exporting

    # Set the environment-specific variables
    case $env_suffix in
        "DEV")
            export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_DEV"
            export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_DEV"
            export AWS_DEFAULT_REGION="$AWS_DEFAULT_REGION_DEV"
            ;;
        "STAGING")
            export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_STAGING"
            export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_STAGING"
            export AWS_DEFAULT_REGION="$AWS_DEFAULT_REGION_STAGING"
            ;;
        "PROD")
            export AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID_PROD"
            export AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY_PROD"
            export AWS_DEFAULT_REGION="$AWS_DEFAULT_REGION_PROD"
            ;;
        *)
            print_error "Invalid environment suffix: $env_suffix"
            exit 1
            ;;
    esac

    # Validate that the required variables are set
    if [[ -z "$AWS_ACCESS_KEY_ID" || -z "$AWS_SECRET_ACCESS_KEY" || -z "$AWS_DEFAULT_REGION" ]]; then
        print_error "Missing required AWS credentials for environment $env_suffix"
        print_info "Required variables: AWS_ACCESS_KEY_ID_$env_suffix, AWS_SECRET_ACCESS_KEY_$env_suffix, AWS_DEFAULT_REGION_$env_suffix"
        exit 1
    fi

    print_success "Loaded AWS credentials for $env_suffix environment"
    print_info "Region: $AWS_DEFAULT_REGION"
    print_info "Access Key: ${AWS_ACCESS_KEY_ID:0:8}..."
}

# Function to validate AWS credentials and permissions
validate_credentials() {
    print_info "Validating AWS credentials..."

    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS credentials are not valid or not configured"
        return 1
    fi

    local caller_identity=$(aws sts get-caller-identity)
    local current_account=$(echo "$caller_identity" | jq -r '.Account' 2>/dev/null || echo "unknown")
    local current_user=$(echo "$caller_identity" | jq -r '.Arn' 2>/dev/null || echo "unknown")

    print_success "AWS credentials validated"
    print_info "Account: $current_account"
    print_info "User/Role: $current_user"

    if [ "$current_account" != "$ACCOUNT_ID" ]; then
        print_warning "Current account ($current_account) doesn't match expected account ($ACCOUNT_ID)"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_error "Aborted by user"
            exit 1
        fi
    fi
}

# Function to check CDK bootstrap permissions
check_cdk_permissions() {
    local region="$1"
    print_info "Checking CDK bootstrap permissions..."

    # Test CloudFormation permissions
    print_info "Testing CloudFormation permissions..."
    if aws cloudformation list-stacks --region "$region" >/dev/null 2>&1; then
        print_success "CloudFormation list permission: OK"
    else
        print_error "CloudFormation list permission: FAILED"
        print_info "You need cloudformation:ListStacks permission"
        return 1
    fi

    # Test S3 permissions (CDK needs to create asset buckets)
    print_info "Testing S3 permissions..."
    if aws s3 ls >/dev/null 2>&1; then
        print_success "S3 list permission: OK"
    else
        print_error "S3 list permission: FAILED"
        print_info "You need s3:ListAllMyBuckets permission"
        return 1
    fi

    # Test IAM permissions (CDK creates roles)
    print_info "Testing IAM permissions..."

    # Debug: Show current AWS identity
    print_info "Current AWS identity:"
    aws sts get-caller-identity 2>/dev/null || print_warning "Could not get caller identity"

    # Skip IAM list test and assume AdministratorAccess is working
    print_success "IAM permissions: Assuming AdministratorAccess is available"
    print_info "CDK bootstrap will validate all permissions during execution"

    # Test if user has the required CDK policies
    local user_arn=$(aws sts get-caller-identity --query 'Arn' --output text)
    local user_name=$(echo "$user_arn" | cut -d'/' -f2)

    print_info "Checking attached policies for user: $user_name"

    # Check for our custom policies that might interfere with bootstrap
    local env_policy_name
    case "$environment" in
        "development") env_policy_name="GameFlexDevelopmentCIPolicy" ;;
        "staging") env_policy_name="GameFlexStagingCIPolicy" ;;
        "production") env_policy_name="GameFlexProductionCIPolicy" ;;
    esac

    local has_env_policy=false
    if aws iam list-attached-user-policies --user-name "$user_name" --query "AttachedPolicies[?PolicyName=='$env_policy_name']" --output text 2>/dev/null | grep -q "$env_policy_name"; then
        print_warning "Found environment policy: $env_policy_name"
        print_warning "This policy has explicit deny statements that may interfere with CDK bootstrap"
        print_info "Recommendation: Temporarily detach this policy during bootstrap"
        echo
        print_info "To detach: aws iam detach-user-policy --user-name $user_name --policy-arn arn:aws:iam::$ACCOUNT_ID:policy/$env_policy_name"
        print_info "To reattach after bootstrap: aws iam attach-user-policy --user-name $user_name --policy-arn arn:aws:iam::$ACCOUNT_ID:policy/$env_policy_name"
        echo
        has_env_policy=true
    fi

    # Check for AdministratorAccess
    local has_admin_access=false

    # Check directly attached policies
    if aws iam list-attached-user-policies --user-name "$user_name" --query "AttachedPolicies[?PolicyName=='AdministratorAccess']" --output text 2>/dev/null | grep -q "AdministratorAccess"; then
        print_success "Found AdministratorAccess policy (directly attached)"
        has_admin_access=true
    fi

    # Check group-based policies
    if [ "$has_admin_access" = false ]; then
        local user_groups=$(aws iam list-groups-for-user --user-name "$user_name" --query "Groups[].GroupName" --output text 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$user_groups" ]; then
            for group in $user_groups; do
                if aws iam list-attached-group-policies --group-name "$group" --query "AttachedPolicies[?PolicyName=='AdministratorAccess']" --output text 2>/dev/null | grep -q "AdministratorAccess"; then
                    print_success "Found AdministratorAccess policy via group: $group"
                    has_admin_access=true
                    break
                fi
            done
        else
            print_info "Cannot check group policies due to permission restrictions"
            if [ "$has_env_policy" = true ]; then
                print_info "This is likely due to the environment policy's deny statements"
            fi
        fi
    fi

    if [ "$has_admin_access" = false ]; then
        print_error "No AdministratorAccess policy found"
        print_info "CDK bootstrap requires AdministratorAccess permissions"
        return 1
    fi

    if [ "$has_env_policy" = true ]; then
        print_warning "Environment policy detected - bootstrap may fail due to explicit deny statements"
        echo
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Please detach the environment policy and run the script again"
            return 1
        fi
    fi

    return 0
}

# Function to suggest policy fixes
suggest_policy_fix() {
    local env_name="$1"
    print_info "CDK Bootstrap Permission Requirements:"
    echo "----------------------------------------"
    echo "CDK bootstrap requires extensive AWS permissions including:"
    echo "  • CloudFormation: Create/Update/Delete stacks"
    echo "  • S3: Create buckets and manage objects"
    echo "  • IAM: Create roles and policies"
    echo "  • SSM: Create parameters"
    echo "  • ECR: Create repositories (for container assets)"
    echo
    print_info "Recommended solutions:"
    echo "1. Attach AdministratorAccess policy temporarily:"
    echo "   aws iam attach-user-policy --user-name YOUR_USERNAME --policy-arn arn:aws:iam::aws:policy/AdministratorAccess"
    echo
    echo "2. Or create a custom CDK bootstrap policy with these permissions:"
    echo "   - cloudformation:*"
    echo "   - s3:*"
    echo "   - iam:*"
    echo "   - ssm:*"
    echo "   - ecr:*"
    echo
    echo "3. After bootstrap, you can remove AdministratorAccess and use the environment-specific policies"
    local policy_name
    case "$env_name" in
        "development") policy_name="GameFlexDevelopmentCIPolicy" ;;
        "staging") policy_name="GameFlexStagingCIPolicy" ;;
        "production") policy_name="GameFlexProductionCIPolicy" ;;
        *) policy_name="GameFlex${env_name}CIPolicy" ;;
    esac
    echo "   from the setup guide ($policy_name)"
    echo "----------------------------------------"
}


# Function to run CDK with debug information
debug_cdk_environment() {
    print_info "CDK Environment Debug Information:"
    echo "----------------------------------------"

    echo "Node.js version:"
    node --version 2>/dev/null || echo "Node.js not found"

    echo "npm version:"
    npm --version 2>/dev/null || echo "npm not found"

    echo "CDK version:"
    cdk --version 2>/dev/null || echo "CDK not found"

    echo "AWS CLI version:"
    aws --version 2>/dev/null || echo "AWS CLI not found"

    echo "Current AWS identity:"
    aws sts get-caller-identity 2>/dev/null || echo "AWS credentials not configured"

    echo "Environment variables:"
    echo "AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-not set}"
    echo "AWS_REGION: ${AWS_REGION:-not set}"
    echo "AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:+***set***}"
    echo "AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:+***set***}"

    echo "----------------------------------------"
}

# Function to bootstrap CDK
bootstrap_cdk() {
    local env_name="$1"
    local region="$2"
    local stack_name="$3"
    local description="$4"
    local qualifier="$5"

    print_info "Starting CDK bootstrap for $env_name environment..."
    print_info "Region: $region"
    print_info "Stack Name: $stack_name"
    print_info "Qualifier: $qualifier"
    echo

    # Check if Node.js and npm are installed
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi

    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi

    # Check if CDK is available
    print_info "Checking CDK installation..."

    # Check if global CDK is available first
    if command -v cdk >/dev/null 2>&1; then
        local cdk_version=$(cdk --version 2>/dev/null || echo "version check failed")
        print_info "CDK Version: $cdk_version"
        print_info "CDK Location: $(which cdk)"
    else
        print_warning "Global CDK not found, checking npx..."

        if ! command -v npx >/dev/null 2>&1; then
            print_error "npx is not installed. Please install Node.js and npm first."
            exit 1
        fi

        local cdk_version=$(npx cdk --version 2>/dev/null || echo "not installed")
        if [ "$cdk_version" = "not installed" ]; then
            print_warning "CDK not found. Installing CDK globally..."
            npm install -g aws-cdk
            cdk_version=$(cdk --version 2>/dev/null || echo "installation failed")
        fi

        print_info "CDK Version: $cdk_version"
    fi
    echo

    # Show debug information
    debug_cdk_environment

    # Create a temporary log file for capturing output
    local log_file="/tmp/cdk-bootstrap-$$.log"

    # Determine the CloudFormation execution policy based on environment
    local cf_execution_policy
    case "$env_name" in
        "development") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexDevelopmentCIPolicy" ;;
        "staging") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexStagingCIPolicy" ;;
        "production") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexProductionCIPolicy" ;;
    esac

    # Run CDK bootstrap with real-time output
    print_info "Running CDK bootstrap command..."
    echo "Command: cdk bootstrap aws://$ACCOUNT_ID/$region --toolkit-stack-name $stack_name --description \"$description\" --qualifier $qualifier --cloudformation-execution-policies $cf_execution_policy --verbose"
    echo
    print_info "Bootstrap output:"
    echo "----------------------------------------"

    # Check if stack already exists first (check both new and old naming patterns)
    print_info "Checking if CDK toolkit stack already exists..."

    # Check for the new stack name
    local existing_stack=$(aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --region "$region" \
        --query 'Stacks[0].StackStatus' \
        --output text 2>/dev/null || echo "NOT_FOUND")

    # Also check for old naming pattern (gameflex-development, etc.)
    local old_stack_name=""
    case $env_name in
        "development") old_stack_name="gameflex-development" ;;
        "staging") old_stack_name="gameflex-staging" ;;
        "production") old_stack_name="gameflex-production" ;;
    esac

    local existing_old_stack="NOT_FOUND"
    if [ -n "$old_stack_name" ]; then
        existing_old_stack=$(aws cloudformation describe-stacks \
            --stack-name "$old_stack_name" \
            --region "$region" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "NOT_FOUND")
    fi

    # Report what we found
    if [ "$existing_stack" != "NOT_FOUND" ]; then
        print_warning "New CDK toolkit stack '$stack_name' already exists with status: $existing_stack"
    fi

    if [ "$existing_old_stack" != "NOT_FOUND" ]; then
        print_warning "Old CDK toolkit stack '$old_stack_name' already exists with status: $existing_old_stack"
        print_info "This old stack may conflict with the new bootstrap process."
    fi

    # Handle existing stacks
    if [ "$existing_old_stack" != "NOT_FOUND" ]; then
        print_error "Found existing old-style CDK stack: '$old_stack_name'"
        print_info "You need to delete this stack before creating the new one."
        echo "Run this command to delete the old stack:"
        echo "  aws cloudformation delete-stack --stack-name $old_stack_name --region $region"
        echo "Then wait for deletion to complete and re-run this script."
        exit 1
    elif [ "$existing_stack" != "NOT_FOUND" ]; then
        print_warning "Stack '$stack_name' already exists with status: $existing_stack"
        echo "Options:"
        echo "1) Use existing stack (recommended)"
        echo "2) Update existing stack"
        echo "3) Force recreate stack"
        read -p "Choose option (1-3): " choice

        case $choice in
            1)
                print_info "Using existing stack."
                # Verify the existing stack is working
                print_info "Verifying existing stack..."
                local stack_status=$(aws cloudformation describe-stacks \
                    --stack-name "$stack_name" \
                    --region "$region" \
                    --query 'Stacks[0].StackStatus' \
                    --output text 2>/dev/null || echo "ERROR")

                if [ "$stack_status" = "CREATE_COMPLETE" ] || [ "$stack_status" = "UPDATE_COMPLETE" ]; then
                    print_success "Existing stack is healthy: $stack_status"
                    return 0
                else
                    print_warning "Existing stack status: $stack_status"
                    print_info "Proceeding with update..."
                fi
                ;;
            2)
                print_info "Will update existing stack..."
                ;;
            3)
                print_warning "Will force recreate stack..."
                FORCE_FLAG="--force"
                ;;
            *)
                print_info "Invalid choice. Using existing stack."
                return 0
                ;;
        esac
    fi

    # Run the CDK bootstrap command with better output handling
    print_info "Executing CDK bootstrap..."

    # Set environment variables for better debugging
    export CDK_DEBUG=true
    export AWS_SDK_LOAD_CONFIG=1

    # Determine the CloudFormation execution policy based on environment
    local cf_execution_policy
    case "$env_name" in
        "development") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexDevelopmentCIPolicy" ;;
        "staging") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexStagingCIPolicy" ;;
        "production") cf_execution_policy="arn:aws:iam::$ACCOUNT_ID:policy/GameFlexProductionCIPolicy" ;;
    esac

    # Create the command as a string for better debugging
    local cmd="cdk bootstrap aws://$ACCOUNT_ID/$region --toolkit-stack-name $stack_name --description \"$description\" --qualifier $qualifier --cloudformation-execution-policies $cf_execution_policy --verbose ${FORCE_FLAG:-}"

    # Run the command and capture both stdout and stderr
    print_info "Running: $cmd"
    echo

    # Use a more direct approach to capture output
    local output
    local exit_code

    # Run the CDK command with simplified execution
    print_info "Running CDK bootstrap command..."
    echo "Command: $cmd"
    echo

    # Create a simple wrapper script to ensure we get output
    local wrapper_script="/tmp/cdk-wrapper-$$.sh"
    cat > "$wrapper_script" << EOF
#!/bin/bash
set -e
export CDK_DEBUG=true
export AWS_SDK_LOAD_CONFIG=1
echo "Starting CDK bootstrap..."
$cmd
echo "CDK bootstrap command completed with exit code: \$?"
EOF

    chmod +x "$wrapper_script"

    # Run the wrapper script
    if bash "$wrapper_script"; then
        local exit_code=0
    else
        local exit_code=$?
    fi

    # Clean up
    rm -f "$wrapper_script"

    echo "----------------------------------------"
    print_info "CDK command exit code: $exit_code"

    if [ "$exit_code" -eq 0 ]; then
        print_success "CDK bootstrap completed successfully!"

        # Verify the stack was created
        print_info "Verifying stack creation..."
        sleep 3

        local stack_status=$(aws cloudformation describe-stacks \
            --stack-name "$stack_name" \
            --region "$region" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "NOT_FOUND")

        if [ "$stack_status" = "CREATE_COMPLETE" ] || [ "$stack_status" = "UPDATE_COMPLETE" ]; then
            print_success "Stack verification successful: $stack_status"

            # Show stack details
            print_info "Stack details:"
            aws cloudformation describe-stacks \
                --stack-name "$stack_name" \
                --region "$region" \
                --query 'Stacks[0].{Name:StackName,Status:StackStatus,Created:CreationTime}' \
                --output table
        else
            print_warning "Stack status: $stack_status"
            print_info "You can check the stack status in the AWS Console"
        fi

    else
        print_error "CDK bootstrap failed with exit code: $exit_code"
        echo

        # Most likely cause is insufficient permissions
        print_warning "CDK bootstrap failure is usually caused by insufficient AWS permissions."
        echo

        # Show policy suggestions
        suggest_policy_fix "$env_name"

        # Try to get more specific error information
        print_info "Checking for existing CDK stacks in region $region..."
        if aws cloudformation list-stacks \
            --region "$region" \
            --query 'StackSummaries[?contains(StackName, `CDK`) && StackStatus != `DELETE_COMPLETE`].{Name:StackName,Status:StackStatus}' \
            --output table 2>/dev/null; then
            echo
        else
            print_warning "Could not list CloudFormation stacks - this confirms permission issues"
        fi

        exit 1
    fi
}

# Main script
main() {
    print_header
    
    # Environment selection
    echo "Select environment to bootstrap:"
    echo "1) Development (us-west-2)"
    echo "2) Staging (us-west-2)"
    echo "3) Production (us-east-1)"
    echo
    read -p "Enter choice (1-3): " env_choice
    
    case $env_choice in
        1)
            ENV_NAME="development"
            ENV_SUFFIX="DEV"
            REGION="us-west-2"
            STACK_NAME="CDKToolkit-GameFlex-Development"
            DESCRIPTION="CDK Toolkit for GameFlex Development Environment"
            QUALIFIER="gfdev"
            ;;
        2)
            ENV_NAME="staging"
            ENV_SUFFIX="STAGING"
            REGION="us-west-2"
            STACK_NAME="CDKToolkit-GameFlex-Staging"
            DESCRIPTION="CDK Toolkit for GameFlex Staging Environment"
            QUALIFIER="gfstaging"
            ;;
        3)
            ENV_NAME="production"
            ENV_SUFFIX="PROD"
            REGION="us-east-1"
            STACK_NAME="CDKToolkit-GameFlex-Production"
            DESCRIPTION="CDK Toolkit for GameFlex Production Environment"
            QUALIFIER="gfprod"
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac

    print_info "Selected: $ENV_NAME environment in $REGION"
    echo

    # Load credentials from .env file
    load_env_vars "$ENV_SUFFIX"
    echo
    
    # Validate credentials
    validate_credentials
    echo

    # Check CDK permissions
    check_cdk_permissions "$REGION"
    echo
    
    # Confirm before proceeding
    print_warning "About to bootstrap CDK with the following settings:"
    echo "  Environment: $ENV_NAME"
    echo "  Region: $REGION"
    echo "  Stack Name: $STACK_NAME"
    echo "  Account ID: $ACCOUNT_ID"
    echo
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Aborted by user"
        exit 1
    fi
    
    echo
    # Bootstrap CDK
    bootstrap_cdk "$ENV_NAME" "$REGION" "$STACK_NAME" "$DESCRIPTION" "$QUALIFIER"
    
    echo
    print_success "CDK bootstrap setup completed for $ENV_NAME environment!"
    print_info "You can now deploy your CDK stacks to this environment."
}

# Check if jq is available (optional, for better JSON parsing)
if ! command -v jq >/dev/null 2>&1; then
    print_warning "jq is not installed. JSON output will be less formatted."
    print_info "Install jq for better output: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    echo
fi

# Run main function
main "$@"
