#!/bin/bash

# GameFlex Migration Script
# This script helps migrate from the old manual setup to the new automated system

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  GameFlex Migration to New Bootstrap System"
    echo "=============================================="
    echo -e "${NC}"
}

# Show migration steps
show_migration_steps() {
    print_header
    
    echo "This script will guide you through migrating to the new automated bootstrap system."
    echo ""
    
    print_info "Migration Steps:"
    echo "1. Backup existing GitLab CI/CD variables"
    echo "2. Configure administrator credentials"
    echo "3. Run the new bootstrap script"
    echo "4. Update GitLab CI/CD variables with new access keys"
    echo "5. Test deployments"
    echo "6. Clean up old IAM users (optional)"
    echo ""
    
    print_warning "IMPORTANT: Have your GitLab CI/CD variables ready to backup!"
    echo ""
    
    read -p "Do you want to continue with the migration? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Migration cancelled."
        exit 0
    fi
}

# Step 1: Backup instructions
backup_gitlab_variables() {
    print_info "Step 1: Backup GitLab CI/CD Variables"
    echo ""
    echo "Before proceeding, backup your current GitLab CI/CD variables:"
    echo ""
    echo "Go to: GitLab Project → Settings → CI/CD → Variables"
    echo ""
    echo "Current variables to backup:"
    echo "- AWS_ACCESS_KEY_ID_DEV"
    echo "- AWS_SECRET_ACCESS_KEY_DEV"
    echo "- AWS_ACCESS_KEY_ID_STAGING"
    echo "- AWS_SECRET_ACCESS_KEY_STAGING"
    echo "- AWS_ACCESS_KEY_ID_PROD"
    echo "- AWS_SECRET_ACCESS_KEY_PROD"
    echo ""
    
    read -p "Have you backed up your GitLab CI/CD variables? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Please backup your variables before continuing."
        exit 1
    fi
    
    print_success "GitLab variables backed up"
}

# Step 2: Configure admin credentials
configure_admin_credentials() {
    print_info "Step 2: Configure Administrator Credentials"
    echo ""
    
    local env_file="../.env"
    
    if [ -f "$env_file" ]; then
        print_info ".env file already exists"
        
        # Check if admin credentials are already configured
        if grep -q "AWS_ADMIN_ACCESS_KEY_ID" "$env_file"; then
            print_success "Administrator credentials already configured in .env"
            return 0
        fi
    else
        print_info "Creating .env file from template..."
        cp "../.env.example" "$env_file"
    fi
    
    print_info "Please update the .env file with your administrator credentials:"
    echo ""
    echo "Required variables:"
    echo "- AWS_ADMIN_ACCESS_KEY_ID=********************"
    echo "- AWS_ADMIN_SECRET_ACCESS_KEY=An5pe0X0vdyPZXWIRbIYu/dcYdBd4o/BMGnp1yke"
    echo "- AWS_ACCOUNT_ID=************"
    echo ""
    
    read -p "Have you updated the .env file with administrator credentials? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Please update the .env file before continuing."
        exit 1
    fi
    
    print_success "Administrator credentials configured"
}

# Step 3: Run bootstrap
run_bootstrap() {
    print_info "Step 3: Run New Bootstrap Script"
    echo ""
    
    print_info "Running the automated bootstrap script..."
    
    if [ -f "./bootstrap.sh" ]; then
        print_info "Starting bootstrap process..."
        ./bootstrap.sh
        print_success "Bootstrap completed successfully"
    else
        print_error "Bootstrap script not found: ./bootstrap.sh"
        exit 1
    fi
}

# Step 4: Update GitLab variables
update_gitlab_variables() {
    print_info "Step 4: Update GitLab CI/CD Variables"
    echo ""
    
    print_warning "IMPORTANT: Update your GitLab CI/CD variables with the new access keys"
    print_warning "that were displayed during the bootstrap process."
    echo ""
    
    echo "Go to: GitLab Project → Settings → CI/CD → Variables"
    echo ""
    echo "Update these variables with the new access keys:"
    echo "- AWS_ACCESS_KEY_ID_DEV"
    echo "- AWS_SECRET_ACCESS_KEY_DEV"
    echo "- AWS_ACCESS_KEY_ID_STAGING"
    echo "- AWS_SECRET_ACCESS_KEY_STAGING"
    echo "- AWS_ACCESS_KEY_ID_PROD"
    echo "- AWS_SECRET_ACCESS_KEY_PROD"
    echo ""
    echo "Make sure to mark them as Protected and Masked."
    echo ""
    
    read -p "Have you updated the GitLab CI/CD variables? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Please update your GitLab variables before testing deployments."
        exit 1
    fi
    
    print_success "GitLab variables updated"
}

# Step 5: Test deployments
test_deployments() {
    print_info "Step 5: Test Deployments"
    echo ""
    
    print_info "Test your deployments to ensure everything works:"
    echo ""
    echo "1. Push a commit to the 'develop' branch to test development deployment"
    echo "2. Create a merge request to test the pipeline"
    echo "3. Check that the deployment succeeds"
    echo ""
    
    read -p "Have you tested your deployments successfully? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Please test your deployments to ensure the migration was successful."
        print_info "If deployments fail, check the GitLab CI/CD logs and verify the variables."
        exit 1
    fi
    
    print_success "Deployments tested successfully"
}

# Step 6: Cleanup old users
cleanup_old_users() {
    print_info "Step 6: Clean Up Old IAM Users (Optional)"
    echo ""
    
    print_warning "You can now clean up old IAM users if they're no longer needed."
    echo ""
    echo "Old users that might exist:"
    echo "- Any manually created CI users"
    echo "- Users with non-standard naming"
    echo ""
    echo "The new system creates these standardized users:"
    echo "- gameflex-ci-development"
    echo "- gameflex-ci-staging"
    echo "- gameflex-ci-production"
    echo ""
    
    read -p "Do you want guidance on cleaning up old users? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        print_info "To clean up old users:"
        echo "1. Go to AWS Console → IAM → Users"
        echo "2. Identify old CI users (not the new gameflex-ci-* users)"
        echo "3. Remove their policies and access keys"
        echo "4. Delete the users"
        echo ""
        print_warning "Only delete users you're sure are no longer needed!"
    fi
    
    print_success "Migration completed!"
}

# Main migration function
main() {
    show_migration_steps
    echo ""
    
    backup_gitlab_variables
    echo ""
    
    configure_admin_credentials
    echo ""
    
    run_bootstrap
    echo ""
    
    update_gitlab_variables
    echo ""
    
    test_deployments
    echo ""
    
    cleanup_old_users
    echo ""
    
    print_success "🎉 Migration to new bootstrap system completed successfully!"
    echo ""
    print_info "Benefits of the new system:"
    echo "- Automated user and policy management"
    echo "- Environment-based security isolation"
    echo "- Simplified setup process"
    echo "- Version-controlled policies"
    echo ""
    print_info "For future updates, simply run: ./setup/bootstrap.sh"
}

# Run main function
main "$@"
