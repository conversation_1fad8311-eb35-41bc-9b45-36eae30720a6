{"Version": "2012-10-17", "Statement": [{"Sid": "CloudFormationAccess", "Effect": "Allow", "Action": ["cloudformation:CreateStack", "cloudformation:UpdateStack", "cloudformation:DeleteStack", "cloudformation:DescribeStacks", "cloudformation:DescribeStackEvents", "cloudformation:DescribeStackResources", "cloudformation:GetTemplate", "cloudformation:ListStacks", "cloudformation:ValidateTemplate", "cloudformation:CreateChangeSet", "cloudformation:DescribeChangeSet", "cloudformation:ExecuteChangeSet", "cloudformation:DeleteChangeSet", "cloudformation:ListChangeSets", "cloudformation:GetStackPolicy", "cloudformation:SetStackPolicy"], "Resource": ["arn:aws:cloudformation:*:*:stack/gameflex-dev-*/*", "arn:aws:cloudformation:*:*:stack/gameflex-development-*/*", "arn:aws:cloudformation:*:*:stack/CDKToolkit-GameFlex-Development/*"]}, {"Sid": "S3Access", "Effect": "Allow", "Action": ["s3:CreateBucket", "s3:DeleteBucket", "s3:GetBucketLocation", "s3:GetBucketPolicy", "s3:PutBucketPolicy", "s3:DeleteBucketPolicy", "s3:GetBucketVersioning", "s3:PutBucketVersioning", "s3:GetBucketEncryption", "s3:PutBucketEncryption", "s3:GetBucketPublicAccessBlock", "s3:PutBucketPublicAccessBlock", "s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket", "s3:GetBucketNotification", "s3:PutBucketNotification"], "Resource": ["arn:aws:s3:::gameflex-dev-*", "arn:aws:s3:::gameflex-dev-*/*", "arn:aws:s3:::gameflex-development-*", "arn:aws:s3:::gameflex-development-*/*", "arn:aws:s3:::cdktoolkit-stagingbucket-*"]}, {"Sid": "LambdaAccess", "Effect": "Allow", "Action": ["lambda:CreateFunction", "lambda:DeleteFunction", "lambda:UpdateFunctionCode", "lambda:UpdateFunctionConfiguration", "lambda:GetFunction", "lambda:ListFunctions", "lambda:InvokeFunction", "lambda:AddPermission", "lambda:RemovePermission", "lambda:GetPolicy", "lambda:CreateEventSourceMapping", "lambda:DeleteEventSourceMapping", "lambda:UpdateEventSourceMapping", "lambda:ListEventSourceMappings", "lambda:TagResource", "lambda:UntagResource", "lambda:ListTags"], "Resource": ["arn:aws:lambda:*:*:function:gameflex-dev-*", "arn:aws:lambda:*:*:function:gameflex-development-*"]}, {"Sid": "DynamoDBAccess", "Effect": "Allow", "Action": ["dynamodb:CreateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:UpdateTable", "dynamodb:ListTables", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource", "dynamodb:PutItem", "dynamodb:GetItem", "dynamodb:UpdateItem", "dynamodb:DeleteItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:BatchGetItem", "dynamodb:BatchWriteItem"], "Resource": ["arn:aws:dynamodb:*:*:table/gameflex-dev-*", "arn:aws:dynamodb:*:*:table/gameflex-development-*", "arn:aws:dynamodb:*:*:table/gameflex-dev-*/index/*", "arn:aws:dynamodb:*:*:table/gameflex-development-*/index/*"]}, {"Sid": "IAMRoleAccess", "Effect": "Allow", "Action": ["iam:CreateRole", "iam:DeleteRole", "iam:GetRole", "iam:UpdateRole", "iam:AttachRolePolicy", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:DeleteRolePolicy", "iam:GetRolePolicy", "iam:ListRolePolicies", "iam:ListAttachedRolePolicies", "iam:TagRole", "iam:UntagRole", "iam:PassRole"], "Resource": ["arn:aws:iam::*:role/gameflex-dev-*", "arn:aws:iam::*:role/gameflex-development-*", "arn:aws:iam::*:role/cdk-gfdev-*"]}, {"Sid": "CognitoAccess", "Effect": "Allow", "Action": ["cognito-idp:CreateUserPool", "cognito-idp:DeleteUserPool", "cognito-idp:DescribeUserPool", "cognito-idp:UpdateUserPool", "cognito-idp:CreateUserPoolClient", "cognito-idp:DeleteUserPoolClient", "cognito-idp:DescribeUserPoolClient", "cognito-idp:UpdateUserPoolClient", "cognito-idp:AdminCreateUser", "cognito-idp:AdminDeleteUser", "cognito-idp:AdminGetUser", "cognito-idp:AdminSetUserPassword", "cognito-idp:ListUsers", "cognito-idp:TagResource", "cognito-idp:UntagResource"], "Resource": ["arn:aws:cognito-idp:*:*:userpool/*"], "Condition": {"StringLike": {"aws:RequestedRegion": ["us-west-2"]}}}, {"Sid": "APIGatewayAccess", "Effect": "Allow", "Action": ["apigateway:*"], "Resource": ["arn:aws:apigateway:*::/restapis", "arn:aws:apigateway:*::/restapis/*"]}, {"Sid": "SecretsManagerAccess", "Effect": "Allow", "Action": ["secretsmanager:<PERSON><PERSON><PERSON><PERSON><PERSON>", "secretsmanager:DeleteSecret", "secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue", "secretsmanager:PutSecretV<PERSON>ue", "secretsmanager:UpdateSecret", "secretsmanager:TagResource", "secretsmanager:UntagResource"], "Resource": ["arn:aws:secretsmanager:*:*:secret:gameflex-dev-*", "arn:aws:secretsmanager:*:*:secret:gameflex-development-*"]}, {"Sid": "LogsAccess", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:DeleteLogGroup", "logs:DescribeLogGroups", "logs:PutRetentionPolicy", "logs:TagLogGroup", "logs:UntagLogGroup"], "Resource": ["arn:aws:logs:*:*:log-group:/aws/lambda/gameflex-dev-*", "arn:aws:logs:*:*:log-group:/aws/lambda/gameflex-development-*"]}, {"Sid": "CDKBootstrapAccess", "Effect": "Allow", "Action": ["iam:CreateRole", "iam:DeleteRole", "iam:GetRole", "iam:AttachRolePolicy", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:DeleteRolePolicy", "iam:PassRole", "sts:<PERSON><PERSON>Role"], "Resource": ["arn:aws:iam::*:role/cdk-*"]}, {"Sid": "RegionRestriction", "Effect": "Allow", "Action": ["sts:GetCallerIdentity", "sts:<PERSON><PERSON>Role"], "Resource": "*", "Condition": {"StringEquals": {"aws:RequestedRegion": ["us-west-2"]}}}, {"Sid": "DenyOtherEnvironments", "Effect": "<PERSON><PERSON>", "Action": "*", "Resource": ["arn:aws:cloudformation:*:*:stack/gameflex-staging-*/*", "arn:aws:cloudformation:*:*:stack/gameflex-prod-*/*", "arn:aws:cloudformation:*:*:stack/gameflex-production-*/*", "arn:aws:s3:::gameflex-staging-*", "arn:aws:s3:::gameflex-prod-*", "arn:aws:s3:::gameflex-production-*", "arn:aws:lambda:*:*:function:gameflex-staging-*", "arn:aws:lambda:*:*:function:gameflex-prod-*", "arn:aws:lambda:*:*:function:gameflex-production-*", "arn:aws:dynamodb:*:*:table/gameflex-staging-*", "arn:aws:dynamodb:*:*:table/gameflex-prod-*", "arn:aws:dynamodb:*:*:table/gameflex-production-*", "arn:aws:iam::*:role/gameflex-staging-*", "arn:aws:iam::*:role/gameflex-prod-*", "arn:aws:iam::*:role/gameflex-production-*", "arn:aws:secretsmanager:*:*:secret:gameflex-staging-*", "arn:aws:secretsmanager:*:*:secret:gameflex-prod-*", "arn:aws:secretsmanager:*:*:secret:gameflex-production-*"]}]}