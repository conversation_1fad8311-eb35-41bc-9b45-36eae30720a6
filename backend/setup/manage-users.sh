#!/bin/bash

# GameFlex User Management Script
# This script manages GitLab CI users and their policies automatically
# Uses administrator credentials to create/update users and attach environment-specific policies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
POLICIES_DIR="$SCRIPT_DIR/policies"

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  GameFlex User Management"
    echo "=============================================="
    echo -e "${NC}"
}

# Load environment variables from .env file
load_env() {
    local env_file="$SCRIPT_DIR/../.env"
    if [ -f "$env_file" ]; then
        print_info "Loading environment variables from .env file..."
        export $(grep -v '^#' "$env_file" | xargs)
        
        # Validate required variables
        if [ -z "$AWS_ADMIN_ACCESS_KEY_ID" ] || [ -z "$AWS_ADMIN_SECRET_ACCESS_KEY" ] || [ -z "$AWS_ACCOUNT_ID" ]; then
            print_error "Missing required AWS administrator credentials in .env file"
            print_info "Required variables: AWS_ADMIN_ACCESS_KEY_ID, AWS_ADMIN_SECRET_ACCESS_KEY, AWS_ACCOUNT_ID"
            exit 1
        fi
        
        # Set AWS credentials for this session
        export AWS_ACCESS_KEY_ID="$AWS_ADMIN_ACCESS_KEY_ID"
        export AWS_SECRET_ACCESS_KEY="$AWS_ADMIN_SECRET_ACCESS_KEY"
        export AWS_DEFAULT_REGION="${AWS_DEFAULT_REGION:-us-west-2}"
        
        print_success "Administrator credentials loaded successfully"
    else
        print_error ".env file not found at $env_file"
        print_info "Please create a .env file with your AWS administrator credentials"
        exit 1
    fi
}

# Check if user exists
user_exists() {
    local username=$1
    aws iam get-user --user-name "$username" &>/dev/null
}

# Check if policy exists
policy_exists() {
    local policy_name=$1
    aws iam get-policy --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$policy_name" &>/dev/null
}

# Create or update IAM policy
create_or_update_policy() {
    local policy_name=$1
    local policy_file=$2
    local description=$3
    
    print_info "Managing policy: $policy_name"
    
    if policy_exists "$policy_name"; then
        print_info "Policy $policy_name exists, updating..."
        
        # Create a new policy version
        local version_id=$(aws iam create-policy-version \
            --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$policy_name" \
            --policy-document "file://$policy_file" \
            --set-as-default \
            --query 'PolicyVersion.VersionId' \
            --output text)
        
        print_success "Policy $policy_name updated to version $version_id"
        
        # Clean up old versions (keep only the latest 5)
        aws iam list-policy-versions \
            --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$policy_name" \
            --query 'Versions[?!IsDefaultVersion]|[5:].[VersionId]' \
            --output text | while read version; do
            if [ -n "$version" ]; then
                aws iam delete-policy-version \
                    --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$policy_name" \
                    --version-id "$version" || true
            fi
        done
    else
        print_info "Creating new policy: $policy_name"
        aws iam create-policy \
            --policy-name "$policy_name" \
            --policy-document "file://$policy_file" \
            --description "$description" \
            --path "/" > /dev/null
        
        print_success "Policy $policy_name created successfully"
    fi
}

# Create or update IAM user
create_or_update_user() {
    local username=$1
    local policy_name=$2
    
    print_info "Managing user: $username"
    
    if user_exists "$username"; then
        print_info "User $username already exists"
    else
        print_info "Creating new user: $username"
        aws iam create-user \
            --user-name "$username" \
            --path "/" > /dev/null
        
        print_success "User $username created successfully"
    fi
    
    # Attach the policy to the user
    print_info "Attaching policy $policy_name to user $username"
    aws iam attach-user-policy \
        --user-name "$username" \
        --policy-arn "arn:aws:iam::$AWS_ACCOUNT_ID:policy/$policy_name" || true
    
    # Generate access keys if they don't exist
    local existing_keys=$(aws iam list-access-keys --user-name "$username" --query 'AccessKeyMetadata[].AccessKeyId' --output text)
    
    if [ -z "$existing_keys" ]; then
        print_info "Generating access keys for user $username"
        local key_output=$(aws iam create-access-key --user-name "$username" --output json)
        
        local access_key_id=$(echo "$key_output" | grep -o '"AccessKeyId": "[^"]*"' | cut -d'"' -f4)
        local secret_access_key=$(echo "$key_output" | grep -o '"SecretAccessKey": "[^"]*"' | cut -d'"' -f4)
        
        print_success "Access keys generated for $username"
        print_warning "IMPORTANT: Store these credentials securely!"
        echo "Access Key ID: $access_key_id"
        echo "Secret Access Key: $secret_access_key"
        echo ""
        print_info "Add these to your GitLab CI/CD variables:"
        case "$username" in
            *development*) 
                echo "AWS_ACCESS_KEY_ID_DEV=$access_key_id"
                echo "AWS_SECRET_ACCESS_KEY_DEV=$secret_access_key"
                ;;
            *staging*) 
                echo "AWS_ACCESS_KEY_ID_STAGING=$access_key_id"
                echo "AWS_SECRET_ACCESS_KEY_STAGING=$secret_access_key"
                ;;
            *production*) 
                echo "AWS_ACCESS_KEY_ID_PROD=$access_key_id"
                echo "AWS_SECRET_ACCESS_KEY_PROD=$secret_access_key"
                ;;
        esac
        echo ""
    else
        print_info "User $username already has access keys"
    fi
}

# Manage environment users
manage_environment() {
    local environment=$1
    
    print_header
    print_info "Managing $environment environment..."
    
    case "$environment" in
        "development"|"dev")
            local username="gameflex-ci-development"
            local policy_name="GameFlexDevelopmentCIPolicy"
            local policy_file="$POLICIES_DIR/development-policy.json"
            local description="GameFlex Development CI/CD Policy - Environment-scoped permissions"
            ;;
        "staging")
            local username="gameflex-ci-staging"
            local policy_name="GameFlexStagingCIPolicy"
            local policy_file="$POLICIES_DIR/staging-policy.json"
            local description="GameFlex Staging CI/CD Policy - Environment-scoped permissions"
            ;;
        "production"|"prod")
            local username="gameflex-ci-production"
            local policy_name="GameFlexProductionCIPolicy"
            local policy_file="$POLICIES_DIR/production-policy.json"
            local description="GameFlex Production CI/CD Policy - Environment-scoped permissions"
            ;;
        *)
            print_error "Unknown environment: $environment"
            print_info "Supported environments: development, staging, production"
            exit 1
            ;;
    esac
    
    # Check if policy file exists
    if [ ! -f "$policy_file" ]; then
        print_error "Policy file not found: $policy_file"
        exit 1
    fi
    
    # Create or update the policy
    create_or_update_policy "$policy_name" "$policy_file" "$description"
    
    # Create or update the user
    create_or_update_user "$username" "$policy_name"
    
    print_success "$environment environment setup completed!"
}

# Manage all environments
manage_all_environments() {
    print_header
    print_info "Managing all environments..."
    
    manage_environment "development"
    echo ""
    manage_environment "staging"
    echo ""
    manage_environment "production"
    
    print_success "All environments setup completed!"
}

# Show usage
show_usage() {
    echo "Usage: $0 [ENVIRONMENT]"
    echo ""
    echo "ENVIRONMENT:"
    echo "  development  - Manage development environment user and policy"
    echo "  staging      - Manage staging environment user and policy"
    echo "  production   - Manage production environment user and policy"
    echo "  all          - Manage all environments (default)"
    echo ""
    echo "Examples:"
    echo "  $0                    # Manage all environments"
    echo "  $0 all               # Manage all environments"
    echo "  $0 development       # Manage only development"
    echo "  $0 staging           # Manage only staging"
    echo "  $0 production        # Manage only production"
}

# Main function
main() {
    local environment="${1:-all}"
    
    # Show usage if help requested
    if [ "$environment" = "-h" ] || [ "$environment" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    # Load environment variables
    load_env
    
    # Validate AWS credentials
    print_info "Validating AWS credentials..."
    if ! aws sts get-caller-identity &>/dev/null; then
        print_error "Invalid AWS credentials or AWS CLI not configured"
        exit 1
    fi
    
    local caller_identity=$(aws sts get-caller-identity --output json)
    local current_user=$(echo "$caller_identity" | grep -o '"Arn": "[^"]*"' | cut -d'"' -f4)
    print_success "Authenticated as: $current_user"
    
    # Manage environments
    case "$environment" in
        "all")
            manage_all_environments
            ;;
        "development"|"dev"|"staging"|"production"|"prod")
            manage_environment "$environment"
            ;;
        *)
            print_error "Unknown environment: $environment"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
