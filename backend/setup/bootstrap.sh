#!/bin/bash

# GameFlex Bootstrap Script
# This script manages the complete bootstrap process:
# 1. Sets up GitLab CI users and policies
# 2. Bootstraps CDK for each environment
# Uses administrator credentials for the entire process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  GameFlex Bootstrap Setup"
    echo "=============================================="
    echo -e "${NC}"
}

# Load environment variables from .env file
load_env() {
    local env_file="$SCRIPT_DIR/../.env"
    if [ -f "$env_file" ]; then
        print_info "Loading environment variables from .env file..."
        export $(grep -v '^#' "$env_file" | xargs)
        
        # Validate required variables
        if [ -z "$AWS_ADMIN_ACCESS_KEY_ID" ] || [ -z "$AWS_ADMIN_SECRET_ACCESS_KEY" ] || [ -z "$AWS_ACCOUNT_ID" ]; then
            print_error "Missing required AWS administrator credentials in .env file"
            print_info "Required variables: AWS_ADMIN_ACCESS_KEY_ID, AWS_ADMIN_SECRET_ACCESS_KEY, AWS_ACCOUNT_ID"
            exit 1
        fi
        
        # Set AWS credentials for this session
        export AWS_ACCESS_KEY_ID="$AWS_ADMIN_ACCESS_KEY_ID"
        export AWS_SECRET_ACCESS_KEY="$AWS_ADMIN_SECRET_ACCESS_KEY"
        export AWS_DEFAULT_REGION="${AWS_DEFAULT_REGION:-us-west-2}"
        
        print_success "Administrator credentials loaded successfully"
    else
        print_error ".env file not found at $env_file"
        print_info "Please create a .env file with your AWS administrator credentials"
        exit 1
    fi
}

# Function to get environment details
get_environment_details() {
    local env_name=$1
    
    case "$env_name" in
        "development"|"dev")
            ENV_NAME="development"
            REGION="us-west-2"
            STACK_NAME="CDKToolkit-GameFlex-Development"
            DESCRIPTION="CDK Toolkit for GameFlex Development Environment"
            QUALIFIER="gfdev"
            ;;
        "staging")
            ENV_NAME="staging"
            REGION="us-west-2"
            STACK_NAME="CDKToolkit-GameFlex-Staging"
            DESCRIPTION="CDK Toolkit for GameFlex Staging Environment"
            QUALIFIER="gfstg"
            ;;
        "production"|"prod")
            ENV_NAME="production"
            REGION="us-east-1"
            STACK_NAME="CDKToolkit-GameFlex-Production"
            DESCRIPTION="CDK Toolkit for GameFlex Production Environment"
            QUALIFIER="gfprd"
            ;;
        *)
            print_error "Unknown environment: $env_name"
            print_info "Supported environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Run user management for environment
manage_users_for_environment() {
    local environment=$1
    
    print_info "Setting up GitLab CI users and policies for $environment..."
    
    # Run the user management script
    if [ -f "$SCRIPT_DIR/manage-users.sh" ]; then
        "$SCRIPT_DIR/manage-users.sh" "$environment"
    else
        print_error "User management script not found: $SCRIPT_DIR/manage-users.sh"
        exit 1
    fi
}

# Bootstrap CDK for environment
bootstrap_cdk_for_environment() {
    local environment=$1
    
    get_environment_details "$environment"
    
    print_info "Bootstrapping CDK for $ENV_NAME environment..."
    print_info "Region: $REGION"
    print_info "Stack: $STACK_NAME"
    
    # Check if stack already exists
    local existing_stack=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$REGION" \
        --query 'Stacks[0].StackStatus' \
        --output text 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$existing_stack" != "NOT_FOUND" ]; then
        print_warning "CDK toolkit stack already exists with status: $existing_stack"
        print_info "Updating existing stack..."
    else
        print_info "Creating new CDK toolkit stack..."
    fi
    
    # Determine the CloudFormation execution policy
    local cf_execution_policy="arn:aws:iam::$AWS_ACCOUNT_ID:policy/GameFlex${ENV_NAME^}CIPolicy"
    
    # Run CDK bootstrap
    local cmd="npx cdk bootstrap aws://$AWS_ACCOUNT_ID/$REGION \
        --toolkit-stack-name $STACK_NAME \
        --description \"$DESCRIPTION\" \
        --qualifier $QUALIFIER \
        --cloudformation-execution-policies $cf_execution_policy \
        --verbose"
    
    print_info "Running CDK bootstrap command..."
    print_info "Command: $cmd"
    
    if eval "$cmd"; then
        print_success "CDK bootstrap completed successfully for $ENV_NAME"
        
        # Verify the stack was created/updated
        local final_status=$(aws cloudformation describe-stacks \
            --stack-name "$STACK_NAME" \
            --region "$REGION" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "NOT_FOUND")
        
        if [[ "$final_status" == "CREATE_COMPLETE" || "$final_status" == "UPDATE_COMPLETE" ]]; then
            print_success "Stack verification successful: $final_status"
        else
            print_warning "Stack status: $final_status"
        fi
    else
        print_error "CDK bootstrap failed for $ENV_NAME"
        exit 1
    fi
}

# Bootstrap single environment
bootstrap_environment() {
    local environment=$1
    
    print_header
    print_info "Bootstrapping $environment environment..."
    
    # Step 1: Manage users and policies
    manage_users_for_environment "$environment"
    
    echo ""
    
    # Step 2: Bootstrap CDK
    bootstrap_cdk_for_environment "$environment"
    
    print_success "$environment environment bootstrap completed!"
}

# Bootstrap all environments
bootstrap_all_environments() {
    print_header
    print_info "Bootstrapping all environments..."
    
    # Step 1: Manage all users and policies
    print_info "Setting up all GitLab CI users and policies..."
    if [ -f "$SCRIPT_DIR/manage-users.sh" ]; then
        "$SCRIPT_DIR/manage-users.sh" "all"
    else
        print_error "User management script not found: $SCRIPT_DIR/manage-users.sh"
        exit 1
    fi
    
    echo ""
    
    # Step 2: Bootstrap CDK for all environments
    print_info "Bootstrapping CDK for all environments..."
    
    bootstrap_cdk_for_environment "development"
    echo ""
    bootstrap_cdk_for_environment "staging"
    echo ""
    bootstrap_cdk_for_environment "production"
    
    print_success "All environments bootstrap completed!"
}

# Show usage
show_usage() {
    echo "Usage: $0 [ENVIRONMENT]"
    echo ""
    echo "This script sets up GitLab CI users, policies, and bootstraps CDK for GameFlex environments."
    echo ""
    echo "ENVIRONMENT:"
    echo "  development  - Bootstrap development environment"
    echo "  staging      - Bootstrap staging environment"
    echo "  production   - Bootstrap production environment"
    echo "  all          - Bootstrap all environments (default)"
    echo ""
    echo "Examples:"
    echo "  $0                    # Bootstrap all environments"
    echo "  $0 all               # Bootstrap all environments"
    echo "  $0 development       # Bootstrap only development"
    echo "  $0 staging           # Bootstrap only staging"
    echo "  $0 production        # Bootstrap only production"
    echo ""
    echo "Prerequisites:"
    echo "  - AWS CLI installed and configured"
    echo "  - Node.js and npm installed"
    echo "  - CDK CLI installed (npm install -g aws-cdk)"
    echo "  - .env file with AWS administrator credentials"
}

# Main function
main() {
    local environment="${1:-all}"
    
    # Show usage if help requested
    if [ "$environment" = "-h" ] || [ "$environment" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    # Load environment variables
    load_env
    
    # Validate AWS credentials
    print_info "Validating AWS credentials..."
    if ! aws sts get-caller-identity &>/dev/null; then
        print_error "Invalid AWS credentials or AWS CLI not configured"
        exit 1
    fi
    
    local caller_identity=$(aws sts get-caller-identity --output json)
    local current_user=$(echo "$caller_identity" | grep -o '"Arn": "[^"]*"' | cut -d'"' -f4)
    print_success "Authenticated as: $current_user"
    
    # Check if CDK is installed
    if ! command -v cdk &> /dev/null; then
        print_error "CDK CLI is not installed"
        print_info "Install it with: npm install -g aws-cdk"
        exit 1
    fi
    
    # Bootstrap environments
    case "$environment" in
        "all")
            bootstrap_all_environments
            ;;
        "development"|"dev"|"staging"|"production"|"prod")
            bootstrap_environment "$environment"
            ;;
        *)
            print_error "Unknown environment: $environment"
            show_usage
            exit 1
            ;;
    esac
    
    echo ""
    print_success "Bootstrap process completed successfully!"
    print_info "You can now use the GitLab CI/CD pipeline to deploy to your environments."
}

# Run main function
main "$@"
