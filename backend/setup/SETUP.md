# GameFlex Backend Setup Guide

This streamlined guide will help you set up the complete CI/CD pipeline for the GameFlex backend using automated user management and environment-based policies.

## Overview

The new bootstrap process automatically:
- ✅ Creates GitLab CI users for each environment (dev, staging, production)
- ✅ Applies environment-specific IAM policies with resource-based restrictions
- ✅ Bootstraps CDK for all environments
- ✅ Generates access keys for GitLab CI/CD variables

## Prerequisites

### 1. AWS Administrator Access
You need AWS administrator credentials for the initial setup. These are used only for bootstrapping and user management.

### 2. Required Tools
- **AWS CLI** - Install and configure with your administrator credentials
- **Node.js** (v18+) and **npm**
- **CDK CLI** - Install with `npm install -g aws-cdk`

## Quick Setup

### Step 1: Configure Administrator Credentials

1. **Copy the environment file**:
   ```bash
   cd backend
   cp .env.example .env
   ```

2. **Update the .env file** with your administrator credentials:
   ```bash
   # AWS Administrator Credentials - FOR BOOTSTRAP ONLY
   AWS_ADMIN_ACCESS_KEY_ID=********************
   AWS_ADMIN_SECRET_ACCESS_KEY=An5pe0X0vdyPZXWIRbIYu/dcYdBd4o/BMGnp1yke
   AWS_ACCOUNT_ID=************
   AWS_DEFAULT_REGION=us-west-2
   ```

### Step 2: Run the Bootstrap Script

The bootstrap script handles everything automatically:

```bash
# Navigate to the backend directory
cd backend

# Run the complete bootstrap process
./setup/bootstrap.sh
```

This will:
1. Create GitLab CI users: `gameflex-ci-development`, `gameflex-ci-staging`, `gameflex-ci-production`
2. Apply environment-specific policies with strict resource restrictions
3. Bootstrap CDK for all environments
4. Generate access keys for each user

### Step 3: Configure GitLab CI/CD Variables

After the bootstrap script completes, it will display the access keys. Add these to your GitLab project:

**Go to GitLab Project → Settings → CI/CD → Variables**

Add the following variables (mark as **Protected** and **Masked**):

#### Development Environment
- `AWS_ACCESS_KEY_ID_DEV` = [Access Key from bootstrap output]
- `AWS_SECRET_ACCESS_KEY_DEV` = [Secret Key from bootstrap output]

#### Staging Environment
- `AWS_ACCESS_KEY_ID_STAGING` = [Access Key from bootstrap output]
- `AWS_SECRET_ACCESS_KEY_STAGING` = [Secret Key from bootstrap output]

#### Production Environment
- `AWS_ACCESS_KEY_ID_PROD` = [Access Key from bootstrap output]
- `AWS_SECRET_ACCESS_KEY_PROD` = [Secret Key from bootstrap output]

## Environment-Specific Bootstrap

You can also bootstrap individual environments:

```bash
# Bootstrap only development
./setup/bootstrap.sh development

# Bootstrap only staging
./setup/bootstrap.sh staging

# Bootstrap only production
./setup/bootstrap.sh production
```

## User Management

To manage users and policies separately:

```bash
# Manage all environments
./setup/manage-users.sh

# Manage specific environment
./setup/manage-users.sh development
./setup/manage-users.sh staging
./setup/manage-users.sh production
```

## Security Features

### Environment Isolation
Each user can only access resources for their specific environment:
- **Development user**: Can only access `gameflex-dev-*` and `gameflex-development-*` resources
- **Staging user**: Can only access `gameflex-staging-*` resources  
- **Production user**: Can only access `gameflex-prod-*` and `gameflex-production-*` resources

### Resource-Based Restrictions
Policies use resource ARN patterns to enforce strict boundaries:
```json
"Resource": [
  "arn:aws:lambda:*:*:function:gameflex-dev-*",
  "arn:aws:dynamodb:*:*:table/gameflex-dev-*",
  "arn:aws:s3:::gameflex-dev-*"
]
```

### Regional Restrictions
- **Development/Staging**: Restricted to `us-west-2`
- **Production**: Restricted to `us-east-1`

### Deny Statements
Explicit deny rules prevent cross-environment access:
```json
"Effect": "Deny",
"Resource": [
  "arn:aws:*:*:*:*staging*",
  "arn:aws:*:*:*:*prod*"
]
```

## Policy Files

Environment-specific policies are stored in `setup/policies/`:
- `development-policy.json` - Development environment permissions
- `staging-policy.json` - Staging environment permissions  
- `production-policy.json` - Production environment permissions

These files are automatically applied when running the bootstrap or user management scripts.

## Troubleshooting

### Bootstrap Fails
1. Verify AWS administrator credentials in `.env`
2. Ensure AWS CLI is configured: `aws sts get-caller-identity`
3. Check CDK installation: `cdk --version`

### Permission Denied
1. Verify the administrator user has sufficient permissions
2. Check that policies were created successfully
3. Ensure access keys are correctly configured in GitLab

### Stack Already Exists
The bootstrap script handles existing stacks automatically. If you need to force recreation:
```bash
# Delete existing stack first
aws cloudformation delete-stack --stack-name CDKToolkit-GameFlex-Development --region us-west-2

# Then re-run bootstrap
./setup/bootstrap.sh development
```

## Next Steps

After completing the setup:

1. **Test the pipeline** by pushing code to your repository
2. **Configure R2 credentials** in your `.env` file for media storage
3. **Set up monitoring** and alerting for your environments
4. **Review security policies** periodically and update as needed

The GitLab CI/CD pipeline will now automatically deploy to the appropriate environments based on your branch and tag strategy.
