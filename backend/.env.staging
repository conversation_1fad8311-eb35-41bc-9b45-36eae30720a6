# GameFlex SAM Backend Environment Variables - STAGING
# This file contains secrets that will be automatically uploaded to AWS Secrets Manager
# Non-sensitive configuration is stored in samconfig.toml

# CloudFlare R2 Configuration - STAGING
# These will be stored in AWS Secrets Manager
R2_ACCOUNT_ID=79ee497f37a6902472563e9f3fe8f451
R2_ACCESS_KEY_ID=ac20ad141b9a1ca9d2470e9b3c663320
R2_SECRET_ACCESS_KEY=62d049b50e3df9701f70b7897e336d87d03a50c4c1c370b8cb28c7f54c37e451
R2_ENDPOINT=https://79ee497f37a6902472563e9f3fe8f451.r2.cloudflarestorage.com
R2_BUCKET_NAME=gameflex-staging
R2_PUBLIC_URL=https://staging.media.gameflex.io

# Application Configuration - STAGING
# These will be stored in AWS Secrets Manager
CLOUDFLARE_API_TOKEN=****************************************
TEST_API_BASE_URL=https://staging.api.gameflex.io
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=StagingTest123!
DEBUG_MODE=staging
FORCE_HTTPS=true
