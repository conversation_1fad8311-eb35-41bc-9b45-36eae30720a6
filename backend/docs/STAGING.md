# GameFlex Backend - Staging Environment

This document describes how to set up and deploy the GameFlex backend to the staging environment.

## Quick Setup Checklist

- [ ] ACM certificate for `*.gameflex.io` in us-west-2
- [ ] CloudFlare R2 bucket `gameflex-staging` created
- [ ] R2 custom domain `staging.media.gameflex.io` configured
- [ ] R2 API credentials obtained
- [ ] AWS credentials configured locally
- [ ] Initialize `samconfig.toml` from example: `./scripts/env-manager.sh init`
- [ ] Update `samconfig.toml` with your actual credentials
- [ ] Environment variable `CERTIFICATE_ARN` set
- [ ] Deploy with `./deploy-staging.sh`
- [ ] Update R2 secret in AWS Secrets Manager
- [ ] Configure CloudFlare DNS records
- [ ] Test endpoints with `./test-staging.sh`

## Overview

The staging environment is designed to mirror production as closely as possible, including:

- **Custom Domain**: `staging.api.gameflex.io`
- **Media Domain**: `staging.media.gameflex.io` 
- **Deletion Protection**: Enabled for all DynamoDB tables and Cognito User Pool
- **AWS Secrets Manager**: Used for R2 credentials instead of environment variables
- **SSL/TLS**: Required with ACM certificate
- **Production-like Configuration**: Same settings as production but with staging data

## Prerequisites

### 1. AWS Certificate Manager (ACM) Certificate

You need an ACM certificate that covers `*.gameflex.io`. This certificate must be in the same region as your deployment (us-west-2).

```bash
# Create or import a certificate for *.gameflex.io
aws acm request-certificate \
    --domain-name "*.gameflex.io" \
    --validation-method DNS \
    --region us-west-2
```

### 2. DNS Configuration (CloudFlare)

Configure your CloudFlare DNS to point the staging domains:

#### API Gateway Domain (`staging.api.gameflex.io`)

After deployment, you'll get a CloudFormation output with the API Gateway domain name. Configure CloudFlare DNS:

1. **Get the API Gateway Domain Name** (from deployment output or AWS Console):
   ```bash
   aws cloudformation describe-stacks \
       --stack-name gameflex-staging \
       --query 'Stacks[0].Outputs[?OutputKey==`ApiDomainName`].OutputValue' \
       --output text
   ```
   This will return something like: `d-abc123xyz.execute-api.us-west-2.amazonaws.com`

2. **Add CNAME Record in CloudFlare**:
   - **Type**: CNAME
   - **Name**: `staging.api`
   - **Target**: `d-abc123xyz.execute-api.us-west-2.amazonaws.com` (from step 1)
   - **TTL**: Auto
   - **Proxy Status**: DNS only (gray cloud) - **Important: Do not proxy this**

#### Media Domain (`staging.media.gameflex.io`)

This points to your CloudFlare R2 bucket with custom domain:

1. **Configure R2 Custom Domain** (in CloudFlare Dashboard):
   - Go to R2 Object Storage → Your bucket (`gameflex-staging`)
   - Click "Settings" → "Custom Domains"
   - Add custom domain: `staging.media.gameflex.io`
   - CloudFlare will automatically create the necessary DNS records

2. **Verify DNS Record** (should be created automatically):
   - **Type**: CNAME
   - **Name**: `staging.media`
   - **Target**: `gameflex-staging.your-account-id.r2.cloudflarestorage.com`
   - **TTL**: Auto
   - **Proxy Status**: Proxied (orange cloud) - **Recommended for CDN benefits**

### 3. CloudFlare R2 Setup

Create a staging R2 bucket and configure the custom domain:

#### Create R2 Bucket
1. **Go to CloudFlare Dashboard** → R2 Object Storage
2. **Create bucket**: `gameflex-staging`
3. **Set bucket permissions**: Public read access for media files

#### Configure Custom Domain
1. **In the bucket settings**, go to "Custom Domains"
2. **Add domain**: `staging.media.gameflex.io`
3. **CloudFlare will automatically**:
   - Create the necessary DNS CNAME record
   - Issue SSL certificate for the domain
   - Enable CDN caching

#### Get R2 Credentials
1. **Go to CloudFlare Dashboard** → R2 Object Storage → Manage R2 API tokens
2. **Create API token** with permissions:
   - Object Read & Write for `gameflex-staging` bucket
   - Account ID: Found in the right sidebar
3. **Save credentials** for later use in AWS Secrets Manager

## Deployment

### 1. Initialize and Configure samconfig.toml

All environment variables are now stored in `samconfig.toml`. Set up the configuration:

```bash
# Initialize samconfig.toml from example (first time only)
./scripts/env-manager.sh init

# Edit samconfig.toml and replace YOUR_* placeholders with actual values
nano samconfig.toml

# Verify staging configuration
./scripts/env-manager.sh show staging

# Test loading staging environment variables
./scripts/env-manager.sh test-load staging
```

The staging configuration in `samconfig.toml` contains:
- Staging API URLs (`https://staging.api.gameflex.io`)
- Staging media URLs (`https://staging.media.gameflex.io`)
- Staging table names (`gameflex-staging-*`)
- AWS Secrets Manager configuration
- Reduced debug settings
- All necessary environment variables

**Important**: `samconfig.toml` is gitignored to protect credentials. Only `samconfig.toml.example` is committed.

### 2. Set Environment Variables

```bash
export CERTIFICATE_ARN="arn:aws:acm:us-west-2:123456789012:certificate/your-cert-id"
export ENVIRONMENT=staging  # Optional: tells scripts to use .env.staging
```

### 3. Deploy to Staging

```bash
cd backend
./deploy-staging.sh
```

The script will:
- Load staging configuration from `samconfig.toml`
- Build the SAM application
- Deploy to the `gameflex-staging` stack
- Create all AWS resources with staging configuration
- Output deployment information
- **Automatically update `samconfig.toml`** with deployment outputs

### 4. Configure R2 Credentials

After deployment, update the AWS Secrets Manager secret with your R2 credentials:

```bash
aws secretsmanager put-secret-value \
    --secret-id 'gameflex-r2-config-staging' \
    --secret-string '{
        "accountId": "YOUR_R2_ACCOUNT_ID",
        "accessKeyId": "YOUR_R2_ACCESS_KEY", 
        "secretAccessKey": "YOUR_R2_SECRET_KEY",
        "endpoint": "https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com",
        "bucketName": "gameflex-staging",
        "publicUrl": "https://staging.media.gameflex.io"
    }'
```

## Architecture Differences from Development

### Environment Variables vs Secrets Manager

**Development**: R2 credentials stored in environment variables
```yaml
R2_ACCOUNT_ID: "direct-value"
R2_ACCESS_KEY_ID: "direct-value"
```

**Staging**: R2 credentials stored in AWS Secrets Manager
```yaml
R2_SECRET_NAME: "gameflex-r2-config-staging"
R2_ACCOUNT_ID: ""  # Empty, retrieved from secrets
```

### Deletion Protection

**Development**: Disabled (tables can be deleted)
```yaml
DeletionProtectionEnabled: false
DeletionPolicy: Delete
```

**Staging**: Enabled (tables protected from accidental deletion)
```yaml
DeletionProtectionEnabled: true
DeletionPolicy: Retain
```

### Custom Domains

**Development**: Uses AWS-generated URLs
```
https://abc123.execute-api.us-west-2.amazonaws.com/v1/
```

**Staging**: Uses custom domains
```
https://staging.api.gameflex.io/
```

## Environment Management

### Configuration Storage

All environment variables are now stored in `samconfig.toml` instead of separate `.env` files:

```bash
# List all environment configurations
./scripts/env-manager.sh list

# Show staging configuration
./scripts/env-manager.sh show staging

# Test loading staging environment variables
./scripts/env-manager.sh test-load staging

# Update staging config from CloudFormation
./scripts/env-manager.sh update-from-stack staging
```

### Configuration Priority

1. **`samconfig.toml`** - Primary configuration source
2. **`.env` files** - Legacy fallback (if samconfig.toml loading fails)

### Benefits of samconfig.toml

- **Version Control**: Can be committed to GitLab (no secrets in development)
- **Environment Isolation**: Clear separation between dev/staging/production
- **SAM Integration**: Native integration with SAM CLI
- **Centralized**: All configuration in one file

## Testing the Deployment

### 1. Automated Testing

Run the comprehensive staging test suite:

```bash
./test-staging.sh
```

This will test:
- API connectivity
- Health endpoints
- Authentication flow
- Media upload endpoints
- Unit and integration tests

### 2. Manual Health Check

```bash
curl https://staging.api.gameflex.io/health
```

### 2. Authentication Test

```bash
# Sign up a test user
curl -X POST https://staging.api.gameflex.io/auth/signup \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "username": "testuser"
    }'
```

### 3. Media Upload Test

```bash
# Get upload URL (requires authentication)
curl -X POST https://staging.api.gameflex.io/media/upload \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" \
    -d '{
        "fileName": "test.jpg",
        "fileType": "image/jpeg",
        "fileSize": 1024
    }'
```

## Monitoring and Logs

### CloudWatch Logs

Lambda function logs are available in CloudWatch:
- `/aws/lambda/gameflex-auth-staging`
- `/aws/lambda/gameflex-posts-staging`
- `/aws/lambda/gameflex-media-staging`
- etc.

### CloudWatch Metrics

Monitor API Gateway and Lambda metrics in the AWS Console.

### X-Ray Tracing

Enable X-Ray tracing for distributed tracing (optional).

## Troubleshooting

### Common Issues

1. **Certificate not found**: Ensure ACM certificate exists in the correct region
2. **DNS not resolving**: Check DNS configuration and propagation
3. **R2 access denied**: Verify R2 credentials in Secrets Manager
4. **Lambda timeout**: Check function timeout settings and dependencies

### CloudFlare-Specific Issues

1. **API Gateway CNAME not working**:
   - Ensure proxy status is "DNS only" (gray cloud)
   - API Gateway requires direct DNS resolution, not CloudFlare proxy
   - Check that the target domain matches the API Gateway domain name

2. **Media domain SSL errors**:
   - CloudFlare automatically provisions SSL for R2 custom domains
   - Wait 5-10 minutes after adding the custom domain
   - Check CloudFlare SSL/TLS settings are set to "Full" or "Full (strict)"

3. **R2 custom domain not working**:
   - Verify the bucket name matches exactly: `gameflex-staging`
   - Ensure the custom domain is properly configured in R2 settings
   - Check that DNS propagation is complete (use `dig` or online DNS tools)

4. **CORS issues with R2**:
   - Configure CORS settings in your R2 bucket if needed
   - CloudFlare proxy can sometimes interfere with CORS headers
   - Consider using "DNS only" mode if CORS issues persist

5. **DNS propagation delays**:
   - CloudFlare DNS changes are usually instant
   - Use `dig staging.api.gameflex.io` to verify DNS resolution
   - Clear local DNS cache if needed: `sudo systemctl flush-dns` (Linux)

### Useful Commands

#### AWS Commands
```bash
# Check stack status
aws cloudformation describe-stacks --stack-name gameflex-staging

# View stack events
aws cloudformation describe-stack-events --stack-name gameflex-staging

# Get secret value
aws secretsmanager get-secret-value --secret-id gameflex-r2-config-staging

# View Lambda logs
aws logs tail /aws/lambda/gameflex-auth-staging --follow

# Get API Gateway domain name for DNS configuration
aws cloudformation describe-stacks \
    --stack-name gameflex-staging \
    --query 'Stacks[0].Outputs[?OutputKey==`ApiDomainName`].OutputValue' \
    --output text
```

#### DNS Verification Commands
```bash
# Check API domain DNS resolution
dig staging.api.gameflex.io CNAME

# Check media domain DNS resolution
dig staging.media.gameflex.io CNAME

# Test API endpoint connectivity
curl -I https://staging.api.gameflex.io/health

# Test media domain connectivity
curl -I https://staging.media.gameflex.io

# Check SSL certificate
openssl s_client -connect staging.api.gameflex.io:443 -servername staging.api.gameflex.io

# Trace DNS resolution path
nslookup staging.api.gameflex.io
nslookup staging.media.gameflex.io
```

#### CloudFlare API Commands (Optional)
```bash
# List DNS records (requires CloudFlare API token)
curl -X GET "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID/dns_records" \
     -H "Authorization: Bearer YOUR_API_TOKEN" \
     -H "Content-Type: application/json"

# Check zone status
curl -X GET "https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID" \
     -H "Authorization: Bearer YOUR_API_TOKEN" \
     -H "Content-Type: application/json"
```

## Cleanup

To delete the staging environment:

```bash
# Delete the CloudFormation stack
aws cloudformation delete-stack --stack-name gameflex-staging

# Note: Resources with Retain policy will not be deleted automatically
```

## Security Considerations

- All DynamoDB tables have deletion protection enabled
- R2 credentials are stored securely in AWS Secrets Manager
- API Gateway uses custom domain with SSL/TLS
- Lambda functions have minimal required permissions
- Cognito User Pool has deletion protection enabled
