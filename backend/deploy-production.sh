#!/bin/bash

# GameFlex CDK Production Deployment Script
# This script deploys the GameFlex backend to production environment using AWS CDK

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="production"
PROJECT_NAME="gameflex"
STACK_NAME="${PROJECT_NAME}-${ENVIRONMENT}"
CDK_TOOLKIT_STACK="CDKToolkit-GameFlex-Production"
CDK_QUALIFIER="gfprod"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Deploy GameFlex backend to production environment using AWS CDK"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -y, --yes      Skip confirmation prompts"
    echo "  --diff         Show diff before deployment"
    echo "  --import       Import existing resources"
    echo ""
    echo "Examples:"
    echo "  $0                    # Deploy to production with confirmation"
    echo "  $0 -y                 # Deploy to production without confirmation"
    echo "  $0 --diff             # Show diff for production deployment"
    echo "  $0 --import           # Import existing production resources"
}

# Parse command line arguments
SKIP_CONFIRMATION=false
SHOW_DIFF=false
IMPORT_RESOURCES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -y|--yes)
            SKIP_CONFIRMATION=true
            shift
            ;;
        --diff)
            SHOW_DIFF=true
            shift
            ;;
        --import)
            IMPORT_RESOURCES=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_warning "🚨 PRODUCTION DEPLOYMENT 🚨"
print_status "Environment: $ENVIRONMENT"
print_status "Stack Name: $STACK_NAME"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured or credentials are invalid"
    print_error "Please run 'aws configure' or set up your AWS credentials"
    exit 1
fi

# Get AWS account and region info
AWS_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
AWS_REGION=$(aws configure get region || echo "us-west-2")

print_status "AWS Account: $AWS_ACCOUNT"
print_status "AWS Region: $AWS_REGION"

# Check if CDK is installed
if ! command -v cdk &> /dev/null; then
    print_error "AWS CDK is not installed"
    print_error "Please install it with: npm install -g aws-cdk"
    exit 1
fi

# Check if CDK is bootstrapped for production environment
if ! aws cloudformation describe-stacks --stack-name $CDK_TOOLKIT_STACK --region $AWS_REGION &> /dev/null; then
    print_error "CDK is not bootstrapped for production environment"
    print_error "Expected CDK toolkit stack: $CDK_TOOLKIT_STACK"
    print_error "Please run the bootstrap script: ./setup/bootstrap-cdk.sh"
    exit 1
fi

print_status "CDK toolkit stack: $CDK_TOOLKIT_STACK"
print_status "CDK qualifier: $CDK_QUALIFIER"

# Function to check if resource exists
resource_exists() {
    local resource_type=$1
    local resource_name=$2

    case $resource_type in
        "cognito-user-pool")
            aws cognito-idp list-user-pools --max-items 60 --query "UserPools[?Name=='$resource_name'].Id" --output text | grep -q .
            ;;
        "dynamodb-table")
            aws dynamodb describe-table --table-name "$resource_name" &> /dev/null
            ;;
        "api-gateway")
            aws apigateway get-rest-apis --query "items[?name=='$resource_name'].id" --output text | grep -q .
            ;;
        *)
            return 1
            ;;
    esac
}

# Function to import existing resources
import_existing_resources() {
    print_status "Checking for existing resources to import..."

    # Check for existing Cognito User Pool
    if resource_exists "cognito-user-pool" "${PROJECT_NAME}-users-${ENVIRONMENT}"; then
        print_warning "Found existing Cognito User Pool: ${PROJECT_NAME}-users-${ENVIRONMENT}"
        print_status "This will be imported during deployment"
    fi

    # Check for existing DynamoDB tables
    local tables=("Posts" "Media" "UserProfiles" "Comments" "Likes" "Follows" "Channels" "ChannelMembers" "Reflexes" "Users")
    for table in "${tables[@]}"; do
        local table_name="${PROJECT_NAME}-${ENVIRONMENT}-${table}"
        if resource_exists "dynamodb-table" "$table_name"; then
            print_warning "Found existing DynamoDB table: $table_name"
            print_status "This will be imported during deployment"
        fi
    done

    # Check for existing API Gateway
    if resource_exists "api-gateway" "${PROJECT_NAME}-api-${ENVIRONMENT}"; then
        print_warning "Found existing API Gateway: ${PROJECT_NAME}-api-${ENVIRONMENT}"
        print_status "This will be imported during deployment"
    fi
}

# Import resources if requested
if $IMPORT_RESOURCES; then
    import_existing_resources
fi

# Ensure required secrets exist
print_status "Ensuring required secrets exist..."
if ! ./scripts/ensure-secrets.sh $PROJECT_NAME $ENVIRONMENT; then
    print_error "Failed to ensure secrets exist"
    exit 1
fi

# Build the project
print_status "Building TypeScript project..."
if ! npm run build; then
    print_error "Failed to build TypeScript project"
    exit 1
fi

# Install dependencies for Lambda functions
print_status "Installing Lambda function dependencies..."
for lambda_dir in src/*/; do
    if [ -f "${lambda_dir}package.json" ]; then
        print_status "Installing dependencies for $(basename $lambda_dir)..."
        (cd "$lambda_dir" && npm install --production)
    fi
done

# Synthesize the stack
print_status "Synthesizing CDK stack..."
cdk synth $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER --quiet

if [ $? -ne 0 ]; then
    print_error "Failed to synthesize CDK stack"
    exit 1
fi

# Show diff if requested
if $SHOW_DIFF; then
    print_status "Showing deployment diff..."
    cdk diff $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER
fi

# Production-specific confirmation
if [ "$SKIP_CONFIRMATION" = false ]; then
    echo ""
    print_warning "🚨 CRITICAL: You are about to deploy to PRODUCTION 🚨"
    print_warning "This will create/update AWS resources in the live production environment"
    print_warning "This deployment will affect real users and incur production costs"
    print_warning "Make sure you have:"
    print_warning "  1. Tested this deployment thoroughly in staging"
    print_warning "  2. Reviewed all changes and their impact"
    print_warning "  3. Coordinated with your team"
    print_warning "  4. Have a rollback plan ready"
    echo ""
    read -p "Are you absolutely sure you want to deploy to PRODUCTION? (yes/NO): " -r
    echo ""
    if [[ ! $REPLY == "yes" ]]; then
        print_status "Production deployment cancelled (type 'yes' to confirm)"
        exit 0
    fi

    echo ""
    print_warning "Final confirmation: Type 'DEPLOY' to proceed with production deployment:"
    read -p "> " -r
    echo ""
    if [[ ! $REPLY == "DEPLOY" ]]; then
        print_status "Production deployment cancelled"
        exit 0
    fi
fi

# Deploy the stack
print_status "🚀 Deploying CDK stack to production..."
cdk deploy $STACK_NAME --context environment=$ENVIRONMENT --context projectName=$PROJECT_NAME --qualifier $CDK_QUALIFIER --require-approval never --progress events

if [ $? -eq 0 ]; then
    print_success "🎉 Production deployment completed successfully!"
    print_status "Stack: $STACK_NAME"
    print_status "Environment: $ENVIRONMENT"

    # Get stack outputs
    print_status "Retrieving stack outputs..."
    aws cloudformation describe-stacks --stack-name $STACK_NAME --region $AWS_REGION --query 'Stacks[0].Outputs' --output table

    print_success "Production environment is live!"
    print_warning "Remember to:"
    print_warning "  1. Monitor the deployment for any issues"
    print_warning "  2. Run smoke tests to verify functionality"
    print_warning "  3. Update monitoring and alerting"
    print_warning "  4. Notify stakeholders of the deployment"

else
    print_error "Production deployment failed"
    print_error "Please check the CloudFormation console for details"
    exit 1
fi
