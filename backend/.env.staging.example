# GameFlex SAM Backend Environment Variables - STAGING
# This file contains secrets that will be automatically uploaded to AWS Secrets Manager
# Non-sensitive configuration is stored in samconfig.toml
# Copy this file to .env.staging and modify with your actual credentials

# CloudFlare R2 Configuration - STAGING
# These will be stored in AWS Secrets Manager
R2_ACCOUNT_ID=YOUR_R2_ACCOUNT_ID
R2_ACCESS_KEY_ID=YOUR_R2_ACCESS_KEY_ID
R2_SECRET_ACCESS_KEY=YOUR_R2_SECRET_ACCESS_KEY
R2_ENDPOINT=https://YOUR_R2_ACCOUNT_ID.r2.cloudflarestorage.com
R2_BUCKET_NAME=gameflex-staging
R2_PUBLIC_URL=https://staging.media.gameflex.io

# Application Configuration - STAGING
# These will be stored in AWS Secrets Manager
CLOUDFLARE_API_TOKEN=YOUR_CLOUDFLARE_API_TOKEN
TEST_API_BASE_URL=https://staging.api.gameflex.io
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=StagingTest123!
DEBUG_MODE=staging
