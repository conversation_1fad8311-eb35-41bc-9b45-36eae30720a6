# Test Environment Configuration
# Update these values for your environment

# API Configuration - Update with your actual API Gateway URL
TEST_API_BASE_URL=https://bllxxn79s2.execute-api.us-west-2.amazonaws.com/v1

# Test User Configuration (will be created during tests)
# EMAIL and USERNAME are auto-generated to be unique - don't set them here
TEST_USER_PASSWORD=TestPassword123!
TEST_USER_FIRST_NAME=Test
TEST_USER_LAST_NAME=User

# Test Configuration
TEST_TIMEOUT=30000

# AWS Configuration (if needed for direct AWS calls)
AWS_REGION=us-west-2
