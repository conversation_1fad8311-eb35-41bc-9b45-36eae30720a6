# GameFlex Backend Environment Variables
# This file contains secrets that will be automatically uploaded to AWS Secrets Manager
# Copy this file to .env and modify with your actual credentials

# AWS Administrator Credentials - FOR BOOTSTRAP ONLY
# These credentials are used for initial setup and user management
AWS_ADMIN_ACCESS_KEY_ID=********************
AWS_ADMIN_SECRET_ACCESS_KEY=An5pe0X0vdyPZXWIRbIYu/dcYdBd4o/BMGnp1yke
AWS_ACCOUNT_ID=************
AWS_DEFAULT_REGION=us-west-2

# CloudFlare R2 Configuration - DEVELOPMENT
# These will be stored in AWS Secrets Manager
R2_ACCOUNT_ID=YOUR_R2_ACCOUNT_ID
R2_ACCESS_KEY_ID=YOUR_R2_ACCESS_KEY_ID
R2_SECRET_ACCESS_KEY=YOUR_R2_SECRET_ACCESS_KEY
R2_ENDPOINT=https://YOUR_R2_ACCOUNT_ID.r2.cloudflarestorage.com
R2_BUCKET_NAME=gameflex-development
R2_PUBLIC_URL=https://pub-YOUR_R2_SUBDOMAIN.r2.dev

# Application Configuration - DEVELOPMENT
# These will be stored in AWS Secrets Manager
CLOUDFLARE_API_TOKEN=YOUR_CLOUDFLARE_API_TOKEN
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=DevTest123!
DEBUG_MODE=development
