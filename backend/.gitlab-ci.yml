# GameFlex Backend GitLab CI/CD Pipeline
# Backend-focused pipeline with SAST, Secrets Detection, and multi-environment deployment
#
# SETUP: Run ./setup/bootstrap.sh to automatically create users and configure environments
# For detailed setup instructions, see setup/SETUP.md

# Global variables
variables:
  # Docker images
  NODE_IMAGE: "node:18-alpine"
  AWS_CLI_IMAGE: "amazon/aws-cli:latest"

  # Node.js configuration
  NODE_VERSION: "18"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"

  # AWS configuration
  AWS_DEFAULT_REGION: "us-west-2"

  # Project configuration
  PROJECT_NAME: "gameflex"

  # Security scanning
  SAST_EXCLUDED_PATHS: "node_modules,build,dist,cdk.out,coverage"

# Pipeline stages
stages:
  - prepare
  - security
  - test
  - build
  - deploy-dev
  - deploy-staging
  - deploy-production

# Global cache configuration
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .npm/

# Include GitLab security templates
include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Secrets-Detection.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
  - template: Security/License-Scanning.gitlab-ci.yml

# ============================================================================
# PREPARATION STAGE
# ============================================================================

# Install backend dependencies
backend-deps:
  stage: prepare
  image: $NODE_IMAGE
  script:
    - npm ci --cache .npm --prefer-offline
  cache:
    key: backend-deps-$CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
      - .npm/
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  only:
    changes:
      - "**/*"
      - .gitlab-ci.yml

# ============================================================================
# SECURITY STAGE
# ============================================================================

# SAST for JavaScript/TypeScript (Backend)
sast:
  stage: security
  extends: .sast-analyzer
  variables:
    SAST_ANALYZER_IMAGE_TAG: "latest"
    SAST_DEFAULT_ANALYZERS: "eslint,nodejs-scan,semgrep"
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"

# Secrets Detection
secrets-detection:
  stage: security
  extends: .secret-analyzer
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_MERGE_REQUEST_IID

# Dependency Scanning for Backend
dependency-scanning:
  stage: security
  extends: .ds-analyzer
  variables:
    DS_DEFAULT_ANALYZERS: "gemnasium"
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - package.json
        - package-lock.json
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - package.json
        - package-lock.json

# License Scanning
license-scanning:
  stage: security
  extends: .license-scanning
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_MERGE_REQUEST_IID

# ============================================================================
# TESTING STAGE
# ============================================================================

# Backend unit tests
backend-test:
  stage: test
  image: $NODE_IMAGE
  dependencies:
    - backend-deps
  script:
    - npm run test:coverage
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
      junit: coverage/junit.xml
    paths:
      - coverage/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"

# Backend integration tests
backend-integration-test:
  stage: test
  image: $NODE_IMAGE
  services:
    - name: localstack/localstack:latest
      alias: localstack
  variables:
    LOCALSTACK_SERVICES: "dynamodb,s3,secretsmanager,cognito-idp"
    AWS_ENDPOINT_URL: "http://localstack:4566"
    AWS_ACCESS_KEY_ID: "test"
    AWS_SECRET_ACCESS_KEY: "test"
    AWS_DEFAULT_REGION: "us-west-2"
  dependencies:
    - backend-deps
  script:
    - echo "Waiting for LocalStack to be ready..."
    - sleep 10
    - npm run test:integration
  artifacts:
    reports:
      junit: coverage/integration-junit.xml
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"
  timeout: 15m

# ============================================================================
# BUILD STAGE
# ============================================================================

# Build backend
backend-build:
  stage: build
  image: $NODE_IMAGE
  dependencies:
    - backend-deps
  script:
    - npm run build
    - npm run synth
  artifacts:
    paths:
      - dist/
      - cdk.out/
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "**/*"



# ============================================================================
# DEVELOPMENT DEPLOYMENT
# ============================================================================

# Deploy backend to development
deploy-backend-dev:
  stage: deploy-dev
  image: $AWS_CLI_IMAGE
  dependencies:
    - backend-build
  before_script:
    - apk add --no-cache nodejs npm
    - npm ci --cache .npm --prefer-offline
  script:
    - export ENVIRONMENT=development
    - ./deploy.sh development
  environment:
    name: development
    url: https://dev.api.gameflex.io
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      changes:
        - "**/*"
  variables:
    AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_DEV
    AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_DEV



# ============================================================================
# STAGING DEPLOYMENT
# ============================================================================

# Deploy backend to staging
deploy-backend-staging:
  stage: deploy-staging
  image: $AWS_CLI_IMAGE
  dependencies:
    - backend-build
  before_script:
    - apk add --no-cache nodejs npm
    - npm ci --cache .npm --prefer-offline
  script:
    - export ENVIRONMENT=staging
    - ./deploy-staging.sh
  environment:
    name: staging
    url: https://staging.api.gameflex.io
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - "**/*"
  variables:
    AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_STAGING
    AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_STAGING
  when: manual



# ============================================================================
# PRODUCTION DEPLOYMENT
# ============================================================================

# Deploy backend to production
deploy-backend-production:
  stage: deploy-production
  image: $AWS_CLI_IMAGE
  dependencies:
    - backend-build
  before_script:
    - apk add --no-cache nodejs npm
    - npm ci --cache .npm --prefer-offline
  script:
    - export ENVIRONMENT=production
    - ./deploy-production.sh
  environment:
    name: production
    url: https://api.gameflex.io
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - "**/*"
  variables:
    AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID_PROD
    AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY_PROD
    AWS_DEFAULT_REGION: "us-east-1"
  when: manual
  allow_failure: false



# ============================================================================
# SECURITY GATES AND QUALITY CHECKS
# ============================================================================

# Security gate - block deployment if vulnerabilities found
security-gate:
  stage: deploy-dev
  image: alpine:latest
  script:
    - echo "Checking security scan results..."
    - |
      if [ -f gl-sast-report.json ]; then
        HIGH_VULNS=$(cat gl-sast-report.json | grep -c '"severity":"High"' || echo "0")
        CRITICAL_VULNS=$(cat gl-sast-report.json | grep -c '"severity":"Critical"' || echo "0")

        if [ "$CRITICAL_VULNS" -gt "0" ]; then
          echo "❌ CRITICAL vulnerabilities found: $CRITICAL_VULNS"
          echo "Deployment blocked due to critical security issues"
          exit 1
        fi

        if [ "$HIGH_VULNS" -gt "5" ]; then
          echo "⚠️  Too many HIGH vulnerabilities found: $HIGH_VULNS"
          echo "Please fix high-severity issues before deployment"
          exit 1
        fi

        echo "✅ Security check passed"
      else
        echo "⚠️  No SAST report found, proceeding with caution"
      fi
    - |
      if [ -f gl-secret-detection-report.json ]; then
        SECRETS=$(cat gl-secret-detection-report.json | grep -c '"category":"secret"' || echo "0")

        if [ "$SECRETS" -gt "0" ]; then
          echo "❌ Secrets detected in code: $SECRETS"
          echo "Deployment blocked due to exposed secrets"
          exit 1
        fi

        echo "✅ No secrets detected"
      else
        echo "⚠️  No secrets detection report found"
      fi
  dependencies:
    - sast
    - secrets-detection
  rules:
    - if: $CI_COMMIT_BRANCH
    - if: $CI_MERGE_REQUEST_IID
  allow_failure: false

# Quality gate - check test coverage
quality-gate:
  stage: deploy-dev
  image: alpine:latest
  script:
    - echo "Checking code quality metrics..."
    - |
      # Check backend test coverage
      if [ -f coverage/coverage-summary.json ]; then
        BACKEND_COVERAGE=$(cat coverage/coverage-summary.json | grep -o '"lines":{"total":[0-9]*,"covered":[0-9]*,"skipped":[0-9]*,"pct":[0-9.]*}' | grep -o '"pct":[0-9.]*' | cut -d':' -f2)
        echo "Backend test coverage: $BACKEND_COVERAGE%"

        if [ $(echo "$BACKEND_COVERAGE < 80" | bc -l) -eq 1 ]; then
          echo "❌ Backend test coverage below 80%: $BACKEND_COVERAGE%"
          exit 1
        fi
      fi
    - echo "✅ Quality checks passed"
  dependencies:
    - backend-test
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG
  allow_failure: false

# ============================================================================
# NOTIFICATION AND CLEANUP
# ============================================================================

# Notify on deployment success
notify-success:
  stage: deploy-production
  image: alpine:latest
  script:
    - echo "🎉 Deployment completed successfully!"
    - echo "Environment: $CI_ENVIRONMENT_NAME"
    - echo "Commit: $CI_COMMIT_SHA"
    - echo "Pipeline: $CI_PIPELINE_URL"
  rules:
    - if: $CI_COMMIT_TAG
  when: on_success

# Notify on deployment failure
notify-failure:
  stage: deploy-production
  image: alpine:latest
  script:
    - echo "❌ Deployment failed!"
    - echo "Environment: $CI_ENVIRONMENT_NAME"
    - echo "Commit: $CI_COMMIT_SHA"
    - echo "Pipeline: $CI_PIPELINE_URL"
    - echo "Please check the pipeline logs for details"
  rules:
    - if: $CI_COMMIT_TAG
  when: on_failure
